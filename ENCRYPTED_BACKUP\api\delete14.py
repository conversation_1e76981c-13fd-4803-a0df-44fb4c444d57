import pickle
import requests
import pandas as pd
import time
import random
import csv
import os
from datetime import datetime

def load_session_data(pickle_file):
    try:
        with open(pickle_file, 'rb') as f:
            return pickle.load(f)
    except Exception as e:
        print(f"Error loading pickle file: {e}")
        return None

def delete_instagram_thread(thread_id, session_data):
    url = f"https://i.instagram.com/api/v1/direct_v2/threads/{thread_id}/hide/"
    
    headers = {
        'User-Agent': session_data['headers']['User-Agent'],
        'Accept-Encoding': session_data['headers']['Accept-Encoding'],
        'Accept': session_data['headers']['Accept'],
        'Connection': session_data['headers']['Connection'],
        'X-IG-App-Locale': session_data['headers']['X-IG-App-Locale'],
        'X-IG-Device-Locale': session_data['headers']['X-IG-Device-Locale'],
        'X-IG-Mapped-Locale': session_data['headers']['X-IG-Mapped-Locale'],
        'X-Pigeon-Session-Id': session_data['headers']['X-Pigeon-Session-Id'],
        'X-Pigeon-Rawclienttime': session_data['headers']['X-Pigeon-Rawclienttime'],
        'X-IG-Bandwidth-Speed-KBPS': session_data['headers']['X-IG-Bandwidth-Speed-KBPS'],
        'X-IG-Bandwidth-TotalBytes-B': session_data['headers']['X-IG-Bandwidth-TotalBytes-B'],
        'X-IG-Bandwidth-TotalTime-MS': session_data['headers']['X-IG-Bandwidth-TotalTime-MS'],
        'X-IG-App-Startup-Country': session_data['headers']['X-IG-App-Startup-Country'],
        'X-Bloks-Version-Id': session_data['headers']['X-Bloks-Version-Id'],
        'X-IG-WWW-Claim': session_data['headers']['X-IG-WWW-Claim'],
        'X-Bloks-Is-Layout-RTL': session_data['headers']['X-Bloks-Is-Layout-RTL'],
        'X-Bloks-Is-Panorama-Enabled': session_data['headers']['X-Bloks-Is-Panorama-Enabled'],
        'X-IG-Device-ID': session_data['headers']['X-IG-Device-ID'],
        'X-IG-Family-Device-ID': session_data['headers']['X-IG-Family-Device-ID'],
        'X-IG-Android-ID': session_data['headers']['X-IG-Android-ID'],
        'X-IG-Timezone-Offset': session_data['headers']['X-IG-Timezone-Offset'],
        'X-IG-Connection-Type': session_data['headers']['X-IG-Connection-Type'],
        'X-IG-Capabilities': session_data['headers']['X-IG-Capabilities'],
        'X-IG-App-ID': session_data['headers']['X-IG-App-ID'],
        'Priority': session_data['headers']['Priority'],
        'Accept-Language': session_data['headers']['Accept-Language'],
        'Host': session_data['headers']['Host'],
        'X-FB-HTTP-Engine': session_data['headers']['X-FB-HTTP-Engine'],
        'X-FB-Client-IP': session_data['headers']['X-FB-Client-IP'],
        'X-FB-Server-Cluster': session_data['headers']['X-FB-Server-Cluster'],
        'Content-Type': session_data['headers']['Content-Type'],
        'Authorization': session_data['headers']['Authorization'],
        'IG-U-DS-USER-ID': session_data['headers']['IG-U-DS-USER-ID'],
        'IG-U-RUR': session_data['headers']['IG-U-RUR'],
        'IG-U-IG-DIRECT-REGION-HINT': session_data['headers']['IG-U-IG-DIRECT-REGION-HINT'],
        'IG-U-SHBID': session_data['headers']['IG-U-SHBID'],
        'IG-U-SHBTS': session_data['headers']['IG-U-SHBTS'],
        'X-Ads-Opt-Out': session_data['headers']['X-Ads-Opt-Out'],
        'X-CM-Bandwidth-KBPS': session_data['headers']['X-CM-Bandwidth-KBPS'],
        'X-CM-Latency': session_data['headers']['X-CM-Latency']
    }

    try:
        response = requests.post(url, headers=headers)
        if response.status_code == 200:
            print(f"Successfully deleted thread {thread_id}")
            return True, "Success"
        else:
            error_msg = f"Failed (Status: {response.status_code})"
            print(f"Failed to delete thread. Status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False, error_msg
    except Exception as e:
        error_msg = f"Error: {str(e)}"
        print(f"Error deleting thread: {e}")
        return False, error_msg

def process_csv_and_delete_threads():
    try:
        timeout_duration = 308
        start_time = time.time()
        
        # Get set of already processed thread_ids from deleted14.csv if it exists
        processed_threads = set()
        log_file_path = r'C:\files\deleted14.csv'
        if os.path.isfile(log_file_path):
            existing_log = pd.read_csv(log_file_path)
            processed_threads = set(existing_log['thread_id'].astype(str))
            print(f"Found {len(processed_threads)} already processed threads")
        
        # Read input CSV
        df = pd.read_csv(r'C:\files\account_data_14.csv')
        
        # Filter for Instagram User entries (case-insensitive)
        df = df[df['title'].str.lower() == "instagram user"]
        
        print(f"Found {len(df)} Instagram User threads to process")
        
        if len(df) == 0:
            print("No Instagram User threads found to delete")
            return

        # Load session data
        session_data = load_session_data('instagram_14.pkl')
        
        if not session_data:
            print("Failed to load session data")
            return

        # Check if file exists and create it with headers if it doesn't
        file_exists = os.path.isfile(log_file_path)
        
        threads_processed = 0
        threads_deleted = 0
        
        # Open the log file in append mode
        with open(log_file_path, 'a', newline='') as log_file:
            log_writer = csv.writer(log_file)
            
            # Write headers only if file is being created for the first time
            if not file_exists:
                log_writer.writerow(['thread_id', 'status', 'timestamp'])
            
            # Process each thread
            for index, row in df.iterrows():
                if time.time() - start_time >= timeout_duration:
                    print(f"\nTimeout reached (30 minutes). Processed {threads_processed} threads, deleted {threads_deleted}")
                    return
                    
                thread_id = str(row['id1'])
                
                # Skip if thread has already been processed
                if thread_id in processed_threads:
                    print(f"Skipping thread {thread_id} - already processed")
                    continue
                
                print(f"Processing thread {thread_id} ({threads_processed + 1}/{len(df)})")
                
                # Attempt to delete the thread
                success, status_message = delete_instagram_thread(thread_id, session_data)
                
                # Log the result
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                log_writer.writerow([thread_id, status_message, timestamp])
                
                # Update counters
                threads_processed += 1
                if success:
                    threads_deleted += 1
                
                # Ensure the log is written immediately
                log_file.flush()
                
                # Add random delay
                delay = random.uniform(2.5, 4.5)
                print(f"Waiting {delay:.2f} seconds before next deletion...")
                time.sleep(delay)

        print(f"\nProcess completed! Processed {threads_processed} threads, successfully deleted {threads_deleted}")

    except Exception as e:
        print(f"Error processing CSV: {e}")

def main():
    print("Starting thread deletion process...")
    print("Script will automatically stop after 30 minutes if not completed.")
    process_csv_and_delete_threads()
    print("Thread deletion process completed.")

if __name__ == "__main__":
    main()