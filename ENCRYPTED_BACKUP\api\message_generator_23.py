import pandas as pd
import requests
import os
import sys
import time
import logging
import json
import asyncio
import aiohttp
from datetime import datetime
from dotenv import load_dotenv


# Set up logging
log_dir = os.path.dirname(os.path.abspath(__file__))
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"message_generator_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")


# Configure logging
logging.basicConfig(
  filename=log_file,
  level=logging.INFO,
  format='%(asctime)s - %(levelname)s - %(message)s',
  datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger()


# Add console handler
console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console.setFormatter(formatter)
logger.addHandler(console)


# Load environment variables
load_dotenv()

# OpenAI API key from environment
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
if not OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY not found in environment variables. Please check your .env file.")


# File to track processed usernames
TRACKING_FILE = r"C:\files\processed_usernames_23.txt"

# Maximum rows to process per execution
MAX_ROWS_PER_RUN = 2000


# Custom prompts directory (match with app.py)
CUSTOM_PROMPTS_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "custom_prompts")
os.makedirs(CUSTOM_PROMPTS_DIR, exist_ok=True)


# Account ID - replace with script number (extract from filename)
ACCOUNT_ID = 23  # Since this is message_generator_23.py


def get_custom_prompt():
   """Get custom prompt for this account, or return default if none exists"""
   prompt_file_path = os.path.join(CUSTOM_PROMPTS_DIR, f"account_{ACCOUNT_ID}.txt")
 
   if os.path.exists(prompt_file_path):
       try:
           with open(prompt_file_path, "r") as f:
               custom_prompt = f.read()
           logger.info(f"Using custom prompt from account_{ACCOUNT_ID}.txt")
           return custom_prompt
       except Exception as e:
           logger.error(f"Error reading custom prompt: {str(e)}")
 
   # Default prompt if no custom prompt exists
   logger.info("Using default prompt")
   return """You are a growth consultant for Instagram creators and businesses, reaching out to potential clients with personalized messages. Your goal is to start a conversation that will lead to a sales opportunity for your Instagram growth services.


PERSONALITY:
- Friendly, warm, and human while maintaining professionalism
- Emotionally connecting but subtly strategic
- Consultant-like rather than marketing-like
- Someone who genuinely wants to help the client unlock their potential


KEY APPROACH:
1. Always reference specific details from their bio (followers, content type, interests)
2. Position yourself as someone who noticed their profile's potential
3. Make the client feel seen and valued
4. SUBTLY guide the conversation towards a sales opportunity
5. Create curiosity about how you could help them grow their account/business
6. Include a hint of FOMO (fear of missing out) without being pushy
7. Always include a clear next step or question to continue the conversation


LANGUAGE GUIDELINES:
- Use words like: potential, opportunity, unlock, grow, expand, reach, impact, achieve, goals
- Phrases to include (modify naturally):
* "We've noticed your profile has significant potential for growth"
* "Your content deserves to reach a wider audience"
* "We help creators like you unlock new opportunities"
* "With the right approach, your account could achieve much more impact"
* "We'd love to share how we could help you reach your goals"


MESSAGE STRUCTURE:
1. Personalized greeting using their username
2. Acknowledge their specific content/interests from bio
3. Compliment something genuine about their profile
4. Introduce the growth opportunity concept
5. Add subtle FOMO element
6. Include clear next step or question


MESSAGE RULES:
- Keep under 100 words total
- Feel like a real person, not automated
- Never make assumptions about details not in their bio
- Include specific follower numbers if mentioned in bio
- Maximum 2-3 emojis (use sparingly and naturally)
- Always end with an invitation to respond


The message should feel like it's from a real consultant who believes in the client's potential, not just a standard marketing message."""


def load_processed_usernames():
  """Load already processed usernames from tracking file and output file"""
  processed = set()
  # Load from tracking file
  if os.path.exists(TRACKING_FILE):
      try:
          with open(TRACKING_FILE, 'r') as f:
              for line in f:
                  username = line.strip().lower()
                  if username:
                      processed.add(username)
          logger.info(f"Loaded {len(processed)} usernames from tracking file")
      except Exception as e:
          logger.error(f"Error loading tracking file: {str(e)}")
  # Also check output file if it exists
  output_file = r"C:\files\bio_messages_23.csv"
  if os.path.exists(output_file):
      try:
          output_df = pd.read_csv(output_file)
          if 'username' in output_df.columns:
              for username in output_df['username']:
                  if not pd.isna(username):
                      processed.add(str(username).strip().lower())
              logger.info(f"Loaded {len(processed)} total usernames from tracking file and output file")
      except Exception as e:
          logger.error(f"Error loading output file: {str(e)}")
  return processed


def save_username_to_tracking(username):
  """Save a processed username to the tracking file"""
  try:
      with open(TRACKING_FILE, 'a') as f:
          f.write(f"{username.strip().lower()}\n")
  except Exception as e:
      logger.error(f"Error updating tracking file: {str(e)}")


def combine_profile_into_bio(row):
  """Combine profile information into a single bio field"""
  try:
      # Start with the original bio
      original_bio = str(row.get('bio', '')) if not pd.isna(row.get('bio', '')) else ''
   
      # Add posts, followers, following info if available
      stats_parts = []
      for field in ['posts', 'followers', 'following']:
          if field in row and not pd.isna(row[field]):
              stats_parts.append(f"{field}{row[field]}")
   
      # Add stats to bio if available
      if stats_parts:
          combined_bio = original_bio
          if combined_bio and not combined_bio.endswith('\n'):
              combined_bio += '\n'
          combined_bio += ''.join(stats_parts)
          return combined_bio
      else:
          return original_bio
  except Exception as e:
      logger.error(f"Error combining profile into bio: {str(e)}")
      return original_bio


async def generate_messages_batch(users_data, model="gpt-4o-mini"):
   """Generate messages in parallel using OpenAI's API"""
   url = "https://api.openai.com/v1/chat/completions"
   headers = {
       "Authorization": f"Bearer {OPENAI_API_KEY}",
       "Content-Type": "application/json"
   }
 
   # Get system prompt (custom or default)
   system_prompt = get_custom_prompt()
 
   async with aiohttp.ClientSession() as session:
       tasks = []
       for user in users_data:
           username = user["username"]
           bio = user["bio"]
         
           data = {
               "model": model,
               "messages": [
                   {"role": "system", "content": system_prompt},
                   {"role": "user", "content": f"Create a personalized Instagram growth consultant message for '{username}' with this bio: '{bio}'"}
               ],
               "max_tokens": 200,
               "temperature": 0.7
           }
         
           tasks.append(process_single_user(session, url, headers, data, user))
     
       return await asyncio.gather(*tasks)


async def process_single_user(session, url, headers, data, user):
   """Process a single user API request"""
   try:
       async with session.post(url, headers=headers, json=data) as response:
           response_json = await response.json()
         
           if response.status == 200:
               message = response_json["choices"][0]["message"]["content"].strip()
               return {
                   'username': user["username"],
                   'id1': user["id1"],
                   'id3': user["id3"],  # Include id3 in the result
                   'title': user["title"],  # Include title in the result
                   'bio': user["bio"],
                   'message': message
               }
           else:
               logger.error(f"Error for {user['username']}: {response_json}")
               return {
                   'username': user["username"],
                   'id1': user["id1"],
                   'id3': user["id3"],  # Include id3 in the result
                   'title': user["title"],  # Include title in the result
                   'bio': user["bio"],
                   'message': f"Error: {response_json.get('error', {}).get('message', 'Unknown error')}"
               }
   except Exception as e:
       logger.error(f"Exception for {user['username']}: {str(e)}")
       return {
           'username': user["username"],
           'id1': user["id1"],
           'id3': user["id3"],  # Include id3 in the result
           'title': user["title"],  # Include title in the result
           'bio': user["bio"],
           'message': f"Error: {str(e)}"
       }


async def check_and_fix_errors(output_file, max_retries=3):
    """Check the output CSV for errors and retry failed messages"""
    try:
        if not os.path.exists(output_file):
            logger.info("Output file doesn't exist yet, nothing to check")
            return
        
        logger.info(f"Checking {output_file} for errors...")
        
        # Read the CSV file
        df = pd.read_csv(output_file)
        
        # Find rows with errors in the message column
        error_mask = df['message'].str.contains('Error:', na=False, case=False)
        error_rows = df[error_mask]
        
        if len(error_rows) == 0:
            logger.info("No errors found in the output file!")
            return
        
        logger.warning(f"Found {len(error_rows)} rows with errors. Attempting to fix...")
        
        # Retry each error row
        for retry_attempt in range(max_retries):
            if len(error_rows) == 0:
                break
                
            logger.info(f"Retry attempt {retry_attempt + 1}/{max_retries}")
            
            # Prepare users data for retry
            users_data = []
            error_indices = []
            
            for idx, row in error_rows.iterrows():
                users_data.append({
                    "username": row["username"],
                    "id1": row["id1"],
                    "id3": row["id3"],
                    "title": row["title"],
                    "bio": row["bio"]
                })
                error_indices.append(idx)
            
            # Retry the failed messages
            retry_results = await generate_messages_batch(users_data)
            
            # Update the dataframe with retry results
            success_count = 0
            for i, result in enumerate(retry_results):
                idx = error_indices[i]
                # Only update if the retry was successful (no error in message)
                if not result['message'].startswith('Error:'):
                    df.at[idx, 'message'] = result['message']
                    logger.info(f"Successfully fixed message for {result['username']}")
                    success_count += 1
            
            if success_count > 0:
                # Save the updated dataframe directly
                df.to_csv(output_file, index=False)
                logger.info(f"Updated {success_count} messages in {output_file}")
            
            # Check for remaining errors
            error_mask = df['message'].str.contains('Error:', na=False, case=False)
            error_rows = df[error_mask]
            
            if len(error_rows) > 0:
                logger.warning(f"Still have {len(error_rows)} errors after retry {retry_attempt + 1}")
                # Add a small delay before next retry
                await asyncio.sleep(2)
        
        # Final check
        final_error_count = len(df[df['message'].str.contains('Error:', na=False, case=False)])
        if final_error_count == 0:
            logger.info("All errors have been fixed successfully!")
        else:
            logger.warning(f"Could not fix {final_error_count} errors after {max_retries} retries")
            
    except Exception as e:
        logger.error(f"Error in check_and_fix_errors: {str(e)}")


async def process_bio_file_async(input_file=r'C:\files\bio_23.csv', output_file=r'C:\files\bio_messages_23.csv', batch_size=20):
   """Process bio file using asynchronous batch processing"""
   try:
       # Check if file exists
       if not os.path.exists(input_file):
           logger.error(f"File not found: {input_file}")
           return 0
     
       logger.info(f"Processing file: {input_file}")
     
       # Log which account ID and prompt we're using
       logger.info(f"Using account ID: {ACCOUNT_ID}")
       prompt_file_path = os.path.join(CUSTOM_PROMPTS_DIR, f"account_{ACCOUNT_ID}.txt")
       if os.path.exists(prompt_file_path):
           logger.info(f"Using custom prompt from: {prompt_file_path}")
       else:
           logger.info("Using default prompt (no custom prompt file found)")
     
       # Load already processed usernames
       processed_usernames = load_processed_usernames()
       logger.info(f"Found {len(processed_usernames)} already processed usernames")
     
       # Read the CSV
       df = pd.read_csv(input_file)
     
       # Check for required columns
       if "username" not in df.columns or "bio" not in df.columns or "id1" not in df.columns or "id3" not in df.columns or "title" not in df.columns:
           logger.error(f"CSV must contain username, bio, id1, id3, and title columns. Available columns: {df.columns.tolist()}")
           return 0
     
       # Filter out already processed usernames
       df['normalized_username'] = df['username'].apply(
           lambda x: str(x).strip().lower() if not pd.isna(x) else "unknown_user"
       )
       df_filtered = df[~df['normalized_username'].isin(processed_usernames)]
     
       if len(df_filtered) == 0:
           logger.info(f"All usernames in {input_file} have already been processed")
           return 0
     
       total_rows = len(df_filtered)
       logger.info(f"Found {total_rows} new users to process")
       
       # Limit to MAX_ROWS_PER_RUN
       rows_to_process = min(total_rows, MAX_ROWS_PER_RUN)
       df_filtered = df_filtered.iloc[:rows_to_process]
       
       logger.info(f"Will process {rows_to_process} users (limit: {MAX_ROWS_PER_RUN} per run)")
     
       # Process in batches
       results = []
       processed_count = 0
       for i in range(0, rows_to_process, batch_size):
           batch = df_filtered.iloc[i:min(i+batch_size, rows_to_process)]
           logger.info(f"Processing batch {i//batch_size + 1}/{(rows_to_process + batch_size - 1)//batch_size} | Progress: {processed_count}/{rows_to_process}")
         
           # Prepare batch data
           users_data = []
           for _, row in batch.iterrows():
               username = row["username"]
               normalized_username = row["normalized_username"]
               id1 = row["id1"]
               id3 = row["id3"]  # Get id3 value
               title = row["title"]  # Get title value
               bio = row["bio"]
             
               # Double-check if already processed
               if normalized_username in processed_usernames:
                   continue
             
               # Combine profile data into bio for message generation
               combined_bio = combine_profile_into_bio(row)
             
               users_data.append({
                   "username": username,
                   "id1": id1,
                   "id3": id3,  # Include id3 in the data
                   "title": title,  # Include title in the data
                   "bio": combined_bio,
                   "normalized_username": normalized_username
               })
         
           # Process batch concurrently
           if users_data:
               logger.info(f"Sending batch of {len(users_data)} requests to OpenAI API")
               batch_results = await generate_messages_batch(users_data)
             
               # Update processed usernames
               for user in users_data:
                   processed_usernames.add(user["normalized_username"])
                   save_username_to_tracking(user["normalized_username"])
                   processed_count += 1
             
               results.extend(batch_results)
             
               # Save batch results
               batch_df = pd.DataFrame(batch_results)
             
               # If output file doesn't exist, create it
               if not os.path.exists(output_file):
                   batch_df.to_csv(output_file, index=False)
                   logger.info(f"Created new file: {output_file}")
               else:
                   # Read existing file
                   existing_df = pd.read_csv(output_file)
                 
                   # Combine with new results
                   combined_df = pd.concat([existing_df, batch_df], ignore_index=True)
                 
                   # Save back to output file
                   combined_df.to_csv(output_file, index=False)
                   logger.info(f"Updated file: {output_file}")
         
           # Add a small delay to be nice to the API
           if i + batch_size < total_rows:
               logger.info(f"Completed batch, moving to next batch...")
               await asyncio.sleep(1)  # Just a small delay between batches
     
       logger.info(f"Completed processing {input_file}")
       logger.info(f"Total new usernames processed in this run: {processed_count}")
       logger.info(f"Reached limit of {MAX_ROWS_PER_RUN} rows per run" if processed_count >= MAX_ROWS_PER_RUN else f"All available users processed")
       
       # Always check for errors in the entire CSV after processing
       logger.info("Checking entire output file for errors...")
       await check_and_fix_errors(output_file)
       
       return processed_count
 
   except Exception as e:
       logger.error(f"Error processing file {input_file}: {str(e)}")
       return 0


# Legacy synchronous method (keeping for compatibility)
def generate_with_openai(bio, username, model="gpt-4o-mini"):
  """Generate message using OpenAI's API with appropriate prompt"""
  try:
      url = "https://api.openai.com/v1/chat/completions"
      headers = {
          "Authorization": f"Bearer {OPENAI_API_KEY}",
          "Content-Type": "application/json"
      }
   
      # Get system prompt (custom or default)
      system_prompt = get_custom_prompt()
   
      data = {
          "model": model,
          "messages": [
              {"role": "system", "content": system_prompt},
              {"role": "user", "content": f"Create a personalized Instagram growth consultant message for '{username}' with this bio: '{bio}'"}
          ],
          "max_tokens": 200,
          "temperature": 0.7
      }
   
      response = requests.post(url, headers=headers, json=data)
      response.raise_for_status()
   
      result = response.json()
      message = result["choices"][0]["message"]["content"].strip()
      return message
  except Exception as e:
      logger.error(f"Error generating with OpenAI for {username}: {str(e)}")
      return f"Error generating message: {str(e)}"


# Legacy synchronous method (keeping for compatibility)
def process_bio_file(input_file=r'C:\files\bio_23.csv', output_file=r'C:\files\bio_messages_23.csv', batch_size=10, delay_seconds=2):
  """Process bio file and generate messages with resume capability"""
  try:
      # Check if file exists
      if not os.path.exists(input_file):
          logger.error(f"File not found: {input_file}")
          return 0
   
      logger.info(f"Processing file: {input_file}")
     
      # Log which account ID and prompt we're using
      logger.info(f"Using account ID: {ACCOUNT_ID}")
      prompt_file_path = os.path.join(CUSTOM_PROMPTS_DIR, f"account_{ACCOUNT_ID}.txt")
      if os.path.exists(prompt_file_path):
          logger.info(f"Using custom prompt from: {prompt_file_path}")
      else:
          logger.info("Using default prompt (no custom prompt file found)")
   
      # Load already processed usernames
      processed_usernames = load_processed_usernames()
      logger.info(f"Found {len(processed_usernames)} already processed usernames")
   
      # Read the CSV
      df = pd.read_csv(input_file)
   
      # Check for required columns
      if "username" not in df.columns or "bio" not in df.columns or "id1" not in df.columns or "id3" not in df.columns or "title" not in df.columns:
          logger.error(f"CSV must contain username, bio, id1, id3, and title columns. Available columns: {df.columns.tolist()}")
          return 0
   
      # Filter out already processed usernames
      df['normalized_username'] = df['username'].apply(
          lambda x: str(x).strip().lower() if not pd.isna(x) else "unknown_user"
      )
      df_filtered = df[~df['normalized_username'].isin(processed_usernames)]
   
      if len(df_filtered) == 0:
          logger.info(f"All usernames in {input_file} have already been processed")
          return 0
   
      total_rows = len(df_filtered)
      logger.info(f"Found {total_rows} new users to process")
      
      # Limit to MAX_ROWS_PER_RUN
      rows_to_process = min(total_rows, MAX_ROWS_PER_RUN)
      df_filtered = df_filtered.iloc[:rows_to_process]
      
      logger.info(f"Will process {rows_to_process} users (limit: {MAX_ROWS_PER_RUN} per run)")
   
      # Process in batches
      processed_count = 0
      for i in range(0, rows_to_process, batch_size):
          batch = df_filtered.iloc[i:min(i+batch_size, rows_to_process)]
          logger.info(f"Processing batch {i//batch_size + 1}/{(rows_to_process + batch_size - 1)//batch_size} | Progress: {processed_count}/{rows_to_process}")
       
          batch_results = []
       
          for _, row in batch.iterrows():
              username = row["username"]
              normalized_username = row["normalized_username"]
              id1 = row["id1"]
              id3 = row["id3"]  # Get id3 value
              title = row["title"]  # Get title value
              bio = row["bio"]
           
              # Double-check if already processed
              if normalized_username in processed_usernames:
                  continue
           
              # Combine profile data into bio for message generation
              combined_bio = combine_profile_into_bio(row)
           
              logger.info(f"Generating message for: {username}")
           
              # Generate message
              message = generate_with_openai(combined_bio, username)
           
              # Create result row with only the specified columns
              result_row = {
                  'username': username,
                  'id1': id1,
                  'id3': id3,  # Include id3 in the results
                  'title': title,  # Include title in the results
                  'bio': bio,
                  'message': message
              }
           
              batch_results.append(result_row)
           
              # Mark as processed
              processed_usernames.add(normalized_username)
              save_username_to_tracking(normalized_username)
              processed_count += 1
       
          # Save batch results
          if batch_results:
              batch_df = pd.DataFrame(batch_results)
           
              # If output file doesn't exist, create it
              if not os.path.exists(output_file):
                  batch_df.to_csv(output_file, index=False)
                  logger.info(f"Created new file: {output_file}")
              else:
                  # Read existing file
                  existing_df = pd.read_csv(output_file)
               
                  # Combine with new results
                  combined_df = pd.concat([existing_df, batch_df], ignore_index=True)
               
                  # Save back to output file
                  combined_df.to_csv(output_file, index=False)
                  logger.info(f"Updated file: {output_file}")
       
          # Add delay between batches to avoid rate limits
          if i + batch_size < total_rows:
              logger.info(f"Sleeping for {delay_seconds} seconds before next batch...")
              time.sleep(delay_seconds)
   
      logger.info(f"Completed processing {input_file}")
      logger.info(f"Total new usernames processed in this run: {processed_count}")
      logger.info(f"Reached limit of {MAX_ROWS_PER_RUN} rows per run" if processed_count >= MAX_ROWS_PER_RUN else f"All available users processed")
      return processed_count
  except Exception as e:
      logger.error(f"Error processing file {input_file}: {str(e)}")
      return 0


if __name__ == "__main__":
   input_file = r"C:\files\bio_23.csv"  # Your input file
   output_file = r"C:\files\bio_messages_23.csv"  # Your output file
 
   # Increase batch size for more efficiency
   batch_size = 20  # Process more users in parallel
   
   logger.info("="*60)
   logger.info(f"MESSAGE GENERATOR CONFIGURATION")
   logger.info(f"Max rows per run: {MAX_ROWS_PER_RUN}")
   logger.info(f"Batch size: {batch_size}")
   logger.info("="*60)
 
   # Check if user wants to only fix errors in existing file
   import sys
   if len(sys.argv) > 1 and sys.argv[1] == "--fix-errors":
       logger.info("Running error checking and fixing only...")
       asyncio.run(check_and_fix_errors(output_file))
   else:
       logger.info(f"Starting message generator with batch processing")
       logger.info(f"Input file: {input_file}")
       logger.info(f"Output file: {output_file}")
 
       # Run the async function
       asyncio.run(process_bio_file_async(input_file, output_file, batch_size))
 
   logger.info("Processing complete!")