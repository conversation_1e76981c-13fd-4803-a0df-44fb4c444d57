import requests
import json
import time
import pyotp
import pickle
import mysql.connector
import logging
import os
import sys
import csv
import random
from datetime import datetime, timedelta
from requests.exceptions import RequestException
from typing import Tuple, Optional, Dict, List, Any




# Configure logging - ERROR level by default, only to file (no console output)
logging.basicConfig(
   level=logging.ERROR,
   format='%(asctime)s - %(levelname)s - %(message)s',
   handlers=[
       logging.FileHandler("instagram_extractor.log")
   ]
)
logger = logging.getLogger(__name__)




# Create a separate logger for thread counts and completion times
# Create progress logger that only logs to file, not console
progress_logger = logging.getLogger("progress")
progress_logger.setLevel(logging.INFO)
progress_file_handler = logging.FileHandler("instagram_extractor.log")
progress_file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
progress_logger.addHandler(progress_file_handler)
progress_logger.propagate = False  # Don't propagate to root logger




def get_credentials_from_db() -> Tuple[str, str, str]:
   """
   Fetch credentials from MySQL database
   Returns: Tuple of (username, password, secretkey)
   """
   try:
       conn = mysql.connector.connect(
           host="localhost",
           user="root",
           password="",
           database="instaadmin",
           port=3306
       )
     
       cursor = conn.cursor()
       cursor.execute("SELECT username, password, secretkey FROM users WHERE id = 2;")
       row = cursor.fetchone()
     
       if row:
           username, password, secretkey = row
           secretkey = secretkey.replace(" ", "")
           return username, password, secretkey
       else:
           logger.error("No credentials found in database")
           raise Exception("No credentials found in database")
         
   except mysql.connector.Error as e:
       logger.error(f"Database error: {str(e)}")
       raise
   finally:
       if 'cursor' in locals():
           cursor.close()
       if 'conn' in locals():
           conn.close()




def load_headers_from_json():
   """Load headers from the JSON file"""
   try:
       with open('instagram_headers.json', 'r') as f:
           headers_list = json.load(f)
           return headers_list[1]
   except FileNotFoundError:
       logger.error("Headers file not found: instagram_headers.json")
       return None
   except json.JSONDecodeError:
       logger.error("JSON decode error in headers file")
       return None
   except Exception as e:
       logger.error(f"Error loading headers: {str(e)}")
       return None




def instagram_login_with_2fa(username: str, password: str, secretkey: str) -> Optional[requests.Session]:
   base_url = 'https://www.instagram.com'
   login_url = f'{base_url}/api/v1/web/accounts/login/ajax/'
   two_factor_url = f'{base_url}/api/v1/web/accounts/login/ajax/two_factor/'
 
   headers = load_headers_from_json()
   if not headers:
       logger.error("Failed to load headers, cannot proceed with login")
       return None
 
   session = requests.Session()
 
   initial_response = session.get(base_url)
   csrf_token = session.cookies.get('csrftoken')
   if csrf_token:
       headers['X-CSRFToken'] = csrf_token




   login_payload = {
       'enc_password': f'#PWD_INSTAGRAM_BROWSER:0:{int(time.time())}:{password}',
       'username': username,
       'queryParams': '{}',
       'optIntoOneTap': 'false',
       'trustedDeviceRecords': '{}',
       'loginAttemptSubmissionCount': '0'
   }




   try:
       login_response = session.post(
           login_url,
           headers=headers,
           data=login_payload
       )
     
       login_data = login_response.json()
     
       if login_data.get('status') != 'ok':
           logger.error(f"Login status not OK: {login_data.get('status', 'unknown')}")
     
       if login_data.get('two_factor_required'):
           csrf_token = session.cookies.get('csrftoken')
           if csrf_token:
               headers['X-CSRFToken'] = csrf_token
         
           two_factor_identifier = login_data['two_factor_info']['two_factor_identifier']
         
           totp = pyotp.TOTP(secretkey)
           verification_code = totp.now()
         
           headers['Referer'] = 'https://www.instagram.com/accounts/login/two_factor?next=%2F'
         
           two_factor_payload = {
               'identifier': two_factor_identifier,
               'queryParams': '{"next":"/"}',
               'trust_signal': 'true',
               'username': username,
               'verification_method': '3',
               'verificationCode': verification_code,
               'device_id': login_data['two_factor_info']['device_id']
           }
         
           time.sleep(2)
         
           two_factor_response = session.post(
               two_factor_url,
               headers=headers,
               data=two_factor_payload
           )
         
           two_factor_data = two_factor_response.json()
         
           if two_factor_response.ok and two_factor_data.get('status') == 'ok':
               save_session(session, headers)
               return session
           else:
               logger.error(f"Two-factor authentication failed: {two_factor_data}")
               return None
       else:
           if login_response.ok and login_data.get('status') == 'ok':
               save_session(session, headers)
               return session
           else:
               logger.error(f"Login failed: {login_data}")
               return None




   except requests.exceptions.RequestException as e:
       logger.error(f"Request exception during login: {str(e)}")
       return None
   except Exception as e:
       logger.error(f"Unexpected error during login: {str(e)}")
       return None




def save_session(session: requests.Session, headers: dict):
   """Save session data to pickle file"""
   try:
       session_data = {
           'cookies': session.cookies.get_dict(),
           'headers': headers
       }
       with open('instagram_session_2.pkl', 'wb') as f:
           pickle.dump(session_data, f)
   except Exception as e:
       logger.error(f"Error saving session: {str(e)}")




def load_session():
   """Load saved session data"""
   try:
       with open('instagram_session_2.pkl', 'rb') as f:
           session_data = pickle.load(f)
           session = requests.Session()
           session.cookies.update(session_data['cookies'])
           headers = session_data['headers']
           session.headers.update(headers)
           return session, headers
   except FileNotFoundError:
       return None, None
   except Exception as e:
       logger.error(f"Error loading session: {str(e)}")
       return None, None




def verify_session(session):
   """Verify if the session is still valid"""
   try:
       response = session.get("https://www.instagram.com/")
       is_valid = response.status_code == 200 and not '"viewer":null' in response.text
       return is_valid
   except Exception as e:
       logger.error(f"Error verifying session: {str(e)}")
       return False




def handle_rate_limiting(response, session, headers):
   """Handle rate limiting with exponential backoff"""
   if response.status_code == 429:
       retry_after = int(response.headers.get('Retry-After', 60))
       logger.error(f"Rate limited. Waiting for {retry_after} seconds")
       time.sleep(retry_after + 5)  # Add 5 seconds buffer
     
       # Refresh CSRF token
       try:
           csrf_response = session.get("https://www.instagram.com/")
           csrf_token = session.cookies.get('csrftoken')
           if csrf_token:
               headers['X-CSRFToken'] = csrf_token
               session.headers.update(headers)
       except Exception as e:
           logger.error(f"Error refreshing CSRF token: {str(e)}")
     
       return True
   return False




def capture_bulk_route_definitions(session, url, headers):
   """Capture all inbox data with improved pagination and error handling"""
   bulk_route_definitions = []
   params = {
       "include_reel": "true",
       "fetch_suggested_count": "0",
       "max_id": "",
       "folder": "0"  # 0 for Primary, 1 for General
   }
 
   threads_seen = set()  # Track seen thread_ids to avoid duplicates
   total_threads = 0
   retry_count = 0
   max_retries = 3
   start_time = time.time()
 
   while True:
       try:
           response = session.get(url, params=params)
         
           if handle_rate_limiting(response, session, headers):
               retry_count += 1
               if retry_count > max_retries:
                   logger.error(f"Max retries ({max_retries}) exceeded due to rate limiting")
                   break
               continue
         
           response.raise_for_status()
           data = response.json()
         
           # Extract threads
           threads = data.get('inbox', {}).get('threads', [])
           new_threads_count = 0
         
           # Process and add each thread that hasn't been seen before
           for thread in threads:
               thread_id = thread.get('thread_id')
               if thread_id not in threads_seen:
                   threads_seen.add(thread_id)
                   new_threads_count += 1
                 
                   # Optimize by minimizing stored data
                   minimal_thread = {
                       "thread_id": thread.get("thread_id"),
                       "thread_v2_id": thread.get("thread_v2_id"),
                       "thread_title": thread.get("thread_title"),
                       "last_activity_at": thread.get("last_activity_at"),
                       "messaging_thread_key": thread.get("messaging_thread_key"),
                       "folder": thread.get("folder", 0),
                       "users": thread.get("users", []),
                       "items": thread.get("items", [])
                   }
                 
                   bulk_route_definitions.append({
                       "timestamp": time.time(),
                       "response_data": {
                           "inbox": {"threads": [minimal_thread]}
                       }
                   })
         
           total_threads += new_threads_count
         
           # Only log total thread count every 20 threads to reduce log volume
           if new_threads_count > 0 and (total_threads % 20 == 0 or new_threads_count >= 20):
               progress_logger.info(f"Extracted {total_threads} threads")
         
           # Check for next page cursor
           if 'inbox' in data and 'oldest_cursor' in data['inbox'] and data['inbox']['oldest_cursor']:
               next_cursor = data['inbox']['oldest_cursor']
             
               # Check if we got a new cursor
               if next_cursor != params['max_id']:
                   params['max_id'] = next_cursor
                   retry_count = 0  # Reset retry count on successful pagination
               else:
                   break
           else:
               break
         
           # Use random delay between 2 and 3 seconds to look more natural
           random_delay = random.uniform(2.0, 3.0)
           time.sleep(random_delay)
         
       except RequestException as e:
           logger.error(f"Request exception: {str(e)}")
           retry_count += 1
         
           if retry_count > max_retries:
               logger.error(f"Max retries ({max_retries}) exceeded")
               break
         
           wait_time = 10 * (2 ** retry_count)
           time.sleep(wait_time)
       except Exception as e:
           logger.error(f"Unexpected error: {str(e)}")
           if len(bulk_route_definitions) > 0:
               break
           retry_count += 1
           if retry_count > max_retries:
               break
           time.sleep(10)
 
   progress_logger.info(f"Finished extracting. Total threads: {total_threads}")
   return bulk_route_definitions




def extract_instagram_data(session, headers):
   """Extract all Instagram inbox data"""
   try:
       start_time = time.time()
       inbox_url = "https://www.instagram.com/direct/inbox/"
       response = session.get(inbox_url)
       if response.status_code != 200:
           logger.error(f"Failed to access inbox page: {response.status_code}")
           return False
     
       bulk_route_url = "https://www.instagram.com/api/v1/direct_v2/inbox/"
       bulk_route_definitions = capture_bulk_route_definitions(session, bulk_route_url, headers)
     
       if bulk_route_definitions:
           timestamp = int(time.time())
           output_file = f'account_data_2.json'
         
           # Speed up by using faster JSON dumping
           with open(output_file, 'w', encoding='utf-8') as f:
               json.dump(bulk_route_definitions, f, ensure_ascii=False)
         
           total_time = time.time() - start_time
           progress_logger.info(f"Data extraction completed in {total_time:.2f} seconds")
           return True
     
       logger.error("No data extracted")
       return False
     
   except Exception as e:
       logger.error(f"Error extracting Instagram data: {str(e)}")
       return False




def unix_to_ist(timestamp):
   """Convert Unix timestamp to IST time"""
   try:
       if isinstance(timestamp, str):
           timestamp = int(timestamp)
       utc_time = datetime.utcfromtimestamp(timestamp / 1000000)
       ist_time = utc_time + timedelta(hours=5, minutes=30)
       return ist_time.strftime('%Y-%m-%d %H:%M:%S')
   except Exception as e:
       logger.error(f"Error converting timestamp {timestamp}: {str(e)}")
       return ""




def extract_instagram_data_to_csv(input_file, output_file_main, output_file_messages):
   """Extract data from JSON and write to CSV files with optimized processing"""
   try:
       start_time = time.time()
     
       # Stream process the JSON file to avoid loading everything into memory
       thread_ids_seen = set()
       thread_count = 0
     
       # Create directories if they don't exist
       os.makedirs(os.path.dirname(output_file_main), exist_ok=True)
       os.makedirs(os.path.dirname(output_file_messages), exist_ok=True)
     
       # Setup main CSV file
       main_csv = open(output_file_main, 'w', newline='', encoding='utf-8')
       main_writer = csv.writer(main_csv)
       main_headers = ["source_file", "id1", "id2", "title", "last_activity_at", "messaging_thread_key",
                  "id3", "full_name", "username", "short_name", "has_anonymous_profile_picture",
                  "is_verified", "is_private", "following", "is_bestie", "is_feed_favorite",
                  "is_restricted", "outgoing_request", "incoming_request", "muting", "blocking",
                  "last_message_timestamp", "last_message_type", "last_message_content"]
       main_writer.writerow(main_headers)
     
       # Setup messages CSV file
       messages_csv = open(output_file_messages, 'w', newline='', encoding='utf-8')
       messages_writer = csv.writer(messages_csv)
       messages_headers = ["source_file", "pk_id", "timestamp", "text"]
       messages_writer.writerow(messages_headers)
     
       message_count = 0
     
       with open(input_file, 'r', encoding='utf-8') as f:
           data = json.load(f)
     
       for response_item in data:
           response_data = response_item.get('response_data', {})
           inbox = response_data.get('inbox', {})
           threads = inbox.get('threads', [])




           for thread in threads:
               thread_id = thread.get("thread_id")
             
               # Skip duplicate threads
               if thread_id in thread_ids_seen:
                   continue
             
               thread_ids_seen.add(thread_id)
               thread_count += 1
             
               # Skip threads that are from General folder (if folder information is available)
               if thread.get('folder', 0) == 1:
                   continue
             
               # Process thread data for CSVs directly instead of storing
               items = thread.get('items', [])
               users = thread.get('users', [])
             
               # Get last message info
               last_message_timestamp = ""
               last_message_type = ""
               last_message_content = ""
               if items:
                   last_item = items[-1]
                   last_message_timestamp = last_item.get("timestamp", "")
                   last_message_type = last_item.get("item_type", "")
                   last_message_content = last_item.get("text", "")
             
               # Write to main CSV
               for user in users:
                   main_writer.writerow([
                       input_file,
                       thread.get("thread_id", ""),
                       thread.get("thread_v2_id", ""),
                       thread.get("thread_title", ""),
                       unix_to_ist(thread.get("last_activity_at", 0)),
                       thread.get("messaging_thread_key", ""),
                       user.get("pk_id", ""),
                       user.get("full_name", ""),
                       user.get("username", ""),
                       user.get("short_name", ""),
                       user.get("has_anonymous_profile_picture", False),
                       user.get("is_verified", False),
                       user.get("is_private", False),
                       '', '', '', '', '', '', '', '', # Friendship status fields (empty)
                       unix_to_ist(last_message_timestamp) if last_message_timestamp else "",
                       last_message_type,
                       last_message_content
                   ])
             
               # Write to messages CSV
               pk_id = users[0].get('pk_id', '') if users else ''
               for item in items:
                   if 'text' in item and item.get('text'):  # Only include items with text
                       messages_writer.writerow([
                           input_file,
                           pk_id,
                           unix_to_ist(item.get('timestamp', 0)),
                           item.get('text', '')
                       ])
                       message_count += 1
     
       # Close file handles
       main_csv.close()
       messages_csv.close()




       total_time = time.time() - start_time
       progress_logger.info(f"Processed {thread_count} threads and {message_count} messages in {total_time:.2f} seconds")




   except Exception as e:
       logger.error(f"Error processing Instagram data: {str(e)}")
       raise




def main():
   try:
       start_time = time.time()
     
       # Get credentials and establish session
       username, password, secretkey = get_credentials_from_db()
       session, headers = load_session()
     
       if session and verify_session(session):
           success = extract_instagram_data(session, headers)
       else:
           session = instagram_login_with_2fa(username, password, secretkey)
           if session:
               # Add random wait time before starting extraction (2-3 seconds)
               random_wait = random.uniform(2.0, 3.0)
               time.sleep(random_wait)
               success = extract_instagram_data(session, session.headers)
           else:
               logger.error("Failed to establish a valid session")
               return
     
       if success:
           input_file = 'account_data_2.json'
           output_file_main = r'C:\files\account_data_2.csv'
           output_file_messages = r'C:\files\insta_data_messages_2.csv'
         
           if os.path.exists(input_file):
               extract_instagram_data_to_csv(input_file, output_file_main, output_file_messages)
               total_time = time.time() - start_time
               progress_logger.info(f"All processing complete in {total_time:.2f} seconds")
           else:
               logger.error(f"Data file not found: {input_file}")
       else:
           logger.error("Data extraction failed")
             
   except Exception as e:
       logger.error(f"Critical error in main process: {str(e)}")
 
   progress_logger.info("Process finished")




if __name__ == "__main__":
   main()