import requests
import uuid
import time
import random
import json
import hashlib
import hmac
import pickle
import pyotp
from typing import Optional, Dict
import pandas as pd
from datetime import datetime, timedelta
import sys
import os

class InstagramAPI:
    def __init__(self):
        self.base_url = "https://i.instagram.com"
        self.session = requests.Session()
        self.headers = self.load_first_json_headers()

    def update_dynamic_parameters(self):
        """Update dynamic parameters to appear as a legitimate client."""
        self.headers.update({
            "X-IG-Bandwidth-Speed-KBPS": f"{random.uniform(2000, 3000):.3f}",
            "X-IG-Bandwidth-TotalBytes-B": str(random.randint(50000000, 90000000)),
            "X-IG-Bandwidth-TotalTime-MS": str(random.randint(2000, 9000)),
            "X-Pigeon-Rawclienttime": str(time.time())
        })
        return self.headers

    def get_default_static_headers(self):
        return {
            "User-Agent": "Instagram 269.********* Android (31/12.0; 320dpi; 1080x2160; <PERSON><PERSON>; <PERSON> 11; jrxclc; kirin980; en_US; 314665256)",
            "Accept-Encoding": "gzip, deflate",
            "Accept": "*/*",
            "Connection": "close",
            "X-IG-App-Locale": "en_US",
            "X-IG-Device-Locale": "en_US",
            "X-IG-Mapped-Locale": "en_US",
            "X-IG-App-Startup-Country": "US",
            "X-Bloks-Version-Id": "ce555e5500576acd8e84a66018f54a05720f2dce29f0bb5a1f97f0c10d6fac48",
            "X-IG-WWW-Claim": "0",
            "X-Bloks-Is-Layout-RTL": "false",
            "X-Bloks-Is-Panorama-Enabled": "true",
            "X-IG-Timezone-Offset": "-14400",
            "X-IG-Connection-Type": "WIFI",
            "X-IG-Capabilities": "3brTvx0=",
            "X-IG-App-ID": "567067343352427",
            "Priority": "u=3",
            "Accept-Language": "en-US",
            "Host": "i.instagram.com",
            "X-FB-HTTP-Engine": "Liger",
            "X-FB-Client-IP": "True",
            "X-FB-Server-Cluster": "True",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "X-Pigeon-Rawclienttime": str(time.time()),
            "IG-INTENDED-USER-ID": "0",
            "X-IG-Nav-Chain": "9MV:self_profile:2,ProfileMediaTabFragment:self_profile:3,9Xf:self_following:4",
            "Authorization": ""
        }

    def load_first_json_headers(self, filename='instagram_headers_1.json'):
        try:
            with open(filename, 'r') as f:
                headers_list = json.load(f)
                dynamic_headers = headers_list[33]
                headers = self.get_default_static_headers()
                headers.update({
                    "X-Pigeon-Session-Id": dynamic_headers["X-Pigeon-Session-Id"],
                    "X-IG-Bandwidth-Speed-KBPS": dynamic_headers["X-IG-Bandwidth-Speed-KBPS"],
                    "X-IG-Bandwidth-TotalBytes-B": dynamic_headers["X-IG-Bandwidth-TotalBytes-B"],
                    "X-IG-Bandwidth-TotalTime-MS": dynamic_headers["X-IG-Bandwidth-TotalTime-MS"],
                    "X-IG-Device-ID": dynamic_headers["X-IG-Device-ID"],
                    "X-IG-Family-Device-ID": dynamic_headers["X-IG-Family-Device-ID"],
                    "X-IG-Android-ID": dynamic_headers["X-IG-Android-ID"],
                    "X-IG-SALT-IDS": dynamic_headers["X-IG-SALT-IDS"]
                })
                return headers
        except Exception as e:
            return {}

    def save_session(self, filename='instagram_34.pkl'):
        try:
            cookie_dict = {}
            for cookie in self.session.cookies:
                cookie_dict[cookie.name] = cookie.value

            session_data = {
                'headers': self.headers,
                'cookies': cookie_dict
            }
            with open(filename, 'wb') as f:
                pickle.dump(session_data, f)
            return True
        except Exception as e:
            return False

    def load_session(self, filename='instagram_34.pkl'):
        try:
            with open(filename, 'rb') as f:
                session_data = pickle.load(f)
            self.session.cookies.clear()
            for name, value in session_data['cookies'].items():
                self.session.cookies.set(name, value, domain='i.instagram.com', path='/')
            self.headers = session_data['headers']
            return True
        except Exception as e:
            return False

    @staticmethod
    def generate_jazoest(symbols: str) -> str:
        amount = sum(ord(s) for s in symbols)
        return f"2{amount}"

    def post_launcher_sync(self):
        # Update dynamic parameters
        self.update_dynamic_parameters()
        
        url = f"{self.base_url}/api/v1/launcher/sync/"
        data = {
            "id": self.headers["X-IG-Device-ID"],
            "server_config_retrieval": "1"
        }
        try:
            response = self.session.post(
                url,
                headers=self.headers,
                data={"signed_body": f"SIGNATURE.{json.dumps(data)}"}
            )
            self.save_session()
            return response
        except Exception as e:
            return None

    def get_qe_sync(self):
        # Update dynamic parameters
        self.update_dynamic_parameters()
        
        url = f"{self.base_url}/api/v1/qe/sync/"
        headers = {
            "User-Agent": "Instagram 269.********* Android (31/12.0; 320dpi; 1080x2160; Xiaomi; Mi 11; jrxclc; kirin980; en_US; 314665256)",
            "Accept-Encoding": "gzip, deflate",
            "Accept": "*/*",
            "Connection": "close",
            "Accept-Language": "en-US",
            "X-IG-Connection-Type": "WIFI",
            "X-IG-Capabilities": "3brTvx0=",
            "X-IG-App-ID": "567067343352427"
        }
        try:
            response = self.session.get(
                url, 
                headers=headers,
                timeout=30
            )
            if response.status_code:
                return response
            return None
        except Exception as e:
            return None

    def login(self, username: str, password: str, verification_code: str = None):
        self.load_session()
        # Update dynamic parameters
        self.update_dynamic_parameters()
        
        url = f"{self.base_url}/api/v1/accounts/login/"
        enc_password = f"#PWD_INSTAGRAM:0:{int(time.time())}:{password}"
        login_data = {
            "jazoest": self.generate_jazoest(self.headers["X-IG-Family-Device-ID"]),
            "country_codes": '[{"country_code":"1","source":["default"]}]',
            "phone_id": self.headers["X-IG-Family-Device-ID"],
            "enc_password": enc_password,
            "username": username,
            "adid": str(uuid.uuid4()),
            "guid": self.headers["X-IG-Device-ID"],
            "device_id": self.headers["X-IG-Android-ID"],
            "google_tokens": "[]",
            "login_attempt_count": "0"
        }

        try:
            response = self.session.post(
                url, 
                headers=self.headers,
                data={"signed_body": f"SIGNATURE.{json.dumps(login_data)}"}
            )
            response_json = response.json()

            if 'two_factor_required' in str(response.content):
                two_factor_info = response_json.get('two_factor_info', {})
                two_factor_identifier = two_factor_info.get('two_factor_identifier')

                if not verification_code:
                    return False

                # Update dynamic parameters again for 2FA
                self.update_dynamic_parameters()
                
                two_factor_data = {
                    "verification_code": verification_code,
                    "phone_id": self.headers["X-IG-Family-Device-ID"],
                    "_csrftoken": self.session.cookies.get('csrftoken', ''),
                    "two_factor_identifier": two_factor_identifier,
                    "username": username,
                    "trust_this_device": "0",
                    "guid": self.headers["X-IG-Device-ID"],
                    "device_id": self.headers["X-IG-Android-ID"],
                    "waterfall_id": str(uuid.uuid4()),
                    "verification_method": "3"
                }

                two_factor_response = self.session.post(
                    f"{self.base_url}/api/v1/accounts/two_factor_login/",
                    headers=self.headers,
                    data={"signed_body": f"SIGNATURE.{json.dumps(two_factor_data)}"}
                )

                if two_factor_response.status_code == 200:
                    if 'ig-set-authorization' in two_factor_response.headers:
                        self.headers['Authorization'] = two_factor_response.headers['ig-set-authorization']
                    if 'ig-set-ig-u-ds-user-id' in two_factor_response.headers:
                        self.headers['IG-U-DS-USER-ID'] = two_factor_response.headers['ig-set-ig-u-ds-user-id']
                    self.save_session()
                    return True
                return False

            if response.status_code == 200:
                if 'ig-set-authorization' in response.headers:
                    self.headers['Authorization'] = response.headers['ig-set-authorization']
                if 'ig-set-ig-u-ds-user-id' in response.headers:
                    self.headers['IG-U-DS-USER-ID'] = response.headers['ig-set-ig-u-ds-user-id']
                self.save_session()
                return True
            return False

        except Exception as e:
            return False

    def get_reels_tray(self):
        self.load_session()
        # Update dynamic parameters
        self.update_dynamic_parameters()
        
        url = f"{self.base_url}/api/v1/feed/reels_tray/"
        next_year = int(time.time()) + 31536000
        current_time = int(time.time())
        user_id = self.headers.get('IG-U-DS-USER-ID')

        if not user_id:
            return None

        additional_headers = {
            "IG-U-DS-USER-ID": user_id,
            "IG-U-IG-DIRECT-REGION-HINT": f"LLA,{user_id},{next_year}:01f7bae7d8b131877d8e0ae1493252280d72f6d0d554447cb1dc9049b6b2c507c08605b7",
            "IG-U-SHBID": f"12695,{user_id},{next_year}:01f778d9c9f7546cf3722578fbf9b85143cd6e5132723e5c93f40f55ca0459c8ef8a0d9f",
            "IG-U-SHBTS": f"{current_time},{user_id},{next_year}:01f7ace11925d0388080078d0282b75b8059844855da27e23c90a362270fddfb3fae7e28",
            "IG-U-RUR": f"RVA,{user_id},{next_year}:01f7f627f9ae4ce2874b2e04463efdb184340968b1b006fa88cb4cc69a942a04201e544c"
        }

        self.headers.update(additional_headers)

        data = {
            "supported_capabilities_new": [
                {"name": "SUPPORTED_SDK_VERSIONS", "value": "119.0,120.0,121.0,122.0,123.0,124.0,125.0,126.0,127.0,128.0,129.0,130.0,131.0,132.0,133.0,134.0,135.0,136.0,137.0,138.0,139.0,140.0,141.0,142.0"},
                {"name": "FACE_TRACKER_VERSION", "value": "14"},
                {"name": "COMPRESSION", "value": "ETC2_COMPRESSION"},
                {"name": "gyroscope", "value": "gyroscope_enabled"}
            ],
            "reason": "cold_start",
            "timezone_offset": "-14400",
            "tray_session_id": str(uuid.uuid4()),
            "request_id": str(uuid.uuid4()),
            "page_size": 50,
            "_uuid": self.headers["X-IG-Device-ID"],
            "reel_tray_impressions": {}
        }

        try:
            response = self.session.post(
                url,
                headers=self.headers,
                data={"signed_body": f"SIGNATURE.{json.dumps(data)}"}
            )
            self.save_session()
            return response
        except Exception as e:
            return None

    def format_message_with_username(self, message: str, username: str) -> str:
        """
        Format message to include @ before username if it appears in the message.
        If username is not in message, prepend @username to the message.
        Handles case-insensitive matching.
        """
        import re
        
        # Check if username exists in message (case-insensitive)
        # Create a pattern that matches the username with word boundaries
        pattern = r'\b' + re.escape(username) + r'\b'
        
        if re.search(pattern, message, re.IGNORECASE):
            # Username exists in message, replace it with @username
            # Use a function to preserve the original case but add @
            def replace_with_at(match):
                return '@' + match.group(0)
            
            formatted_message = re.sub(pattern, replace_with_at, message, flags=re.IGNORECASE)
        else:
            # Username not in message, prepend @username
            formatted_message = f"@{username} {message}"
        
        return formatted_message
    
    def get_feed_timeline(self):
        self.load_session()
        # Update dynamic parameters
        self.update_dynamic_parameters()
        
        url = f"{self.base_url}/api/v1/feed/timeline/"
        mid = hashlib.md5(self.headers["X-IG-Device-ID"].encode()).hexdigest()

        additional_headers = {
            "X-Ads-Opt-Out": "0",
            "X-DEVICE-ID": self.headers["X-IG-Device-ID"],
            "X-CM-Bandwidth-KBPS": "-1.000",
            "X-CM-Latency": "5",
            "X-IG-App-Startup-Country": "US",
            "X-IG-WWW-Claim": "0",
            "X-MID": mid,
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "IG-U-DS-USER-ID": self.headers.get("IG-U-DS-USER-ID", ""),
            "IG-U-RUR": self.headers.get("IG-U-RUR", ""),
            "Authorization": self.headers.get("Authorization", "")
        }

        self.headers.update(additional_headers)

        csrf_token = self.session.cookies.get('csrftoken', '')
        if not csrf_token:
            csrf_token = hashlib.md5(str(time.time()).encode()).hexdigest()

        if not self.session.cookies:
            self.session.cookies.set('mid', mid, domain='i.instagram.com', path='/')
            self.session.cookies.set('csrftoken', csrf_token, domain='i.instagram.com', path='/')

        data = {
            "device_id": self.headers["X-IG-Android-ID"],
            "_uuid": self.headers["X-IG-Device-ID"],
            "_csrftoken": csrf_token,
            "is_prefetch": "0",
            "phone_id": self.headers["X-IG-Family-Device-ID"],
            "is_pull_to_refresh": "0",
            "timezone_offset": "-14400",
            "priority": "1",
            "seen_posts": "[]",
            "unseen_posts": "[]",
            "is_charging": "1",
            "is_dark_mode": "1",
            "will_sound_on": "0",
            "session_id": str(uuid.uuid4()),
            "bloks_versioning_id": self.headers["X-Bloks-Version-Id"],
            "client_session_id": str(uuid.uuid4()),
            "battery_level": "100",
            "is_async_ads_rti": "0",
            "is_async_ads_double_request": "0",
            "rti_delivery_backend": "0",
            "is_async_ads_in_headload_enabled": "0"
        }

        try:
            signed_body = f"SIGNATURE.{json.dumps(data)}"
            response = self.session.post(
                url,
                headers=self.headers,
                data={"signed_body": signed_body}
            )
            self.save_session()
            return response if response.status_code == 200 else None
        except Exception as e:
            return None
        
    def send_direct_message(self, thread_id: str, username: str, custom_message: str):
        try:
            # Load the session and update dynamic parameters before sending message
            self.load_session()
            self.update_dynamic_parameters()
            
            # Format the message with proper @ mention
            formatted_message = self.format_message_with_username(custom_message, username)
            
            timestamp = str(int(time.time() * 1000))
            url = f"{self.base_url}/api/v1/direct_v2/threads/broadcast/text/"
            
            payload = {
                "_uuid": self.headers["X-IG-Device-ID"],
                "device_id": self.headers["X-IG-Android-ID"],
                "action": "send_item",
                "is_x_transport_forward": "false",
                "send_silently": "false",
                "is_shh_mode": "0",
                "send_attribution": "message_button",
                "client_context": timestamp,
                "mutation_token": timestamp,
                "btt_dual_send": "false",
                "nav_chain": "1qT:feed_timeline:1,1qT:feed_timeline:2,1qT:feed_timeline:3,7Az:direct_inbox:4,7Az:direct_inbox:5,5rG:direct_thread:7",
                "is_ae_dual_send": "false",
                "offline_threading_id": timestamp,
                "text": formatted_message,
                "thread_ids": f"[{thread_id}]"
            }
            response = self.session.post(
                url,
                headers=self.headers,
                data=payload
            )
            
            # Save session after sending message
            self.save_session()
            
            return response.status_code == 200, response
        except Exception as e:
            return False, None

    # Add user_id method for fallback
    def send_direct_message_by_user_id(self, user_id: str, username: str, custom_message: str):
        try:
            # Load the session and update dynamic parameters before sending message
            self.load_session()
            self.update_dynamic_parameters()
            
            # Format the message with proper @ mention
            formatted_message = self.format_message_with_username(custom_message, username)
            
            timestamp = str(int(time.time() * 1000))
            url = f"{self.base_url}/api/v1/direct_v2/threads/broadcast/text/"
            
            # The key difference here is using recipient_users instead of thread_ids
            payload = {
                "_uuid": self.headers["X-IG-Device-ID"],
                "device_id": self.headers["X-IG-Android-ID"],
                "action": "send_item",
                "is_x_transport_forward": "false",
                "send_silently": "false",
                "is_shh_mode": "0",
                "send_attribution": "message_button",
                "client_context": timestamp,
                "mutation_token": timestamp,
                "btt_dual_send": "false",
                "nav_chain": "1qT:feed_timeline:1,1qT:feed_timeline:2,1qT:feed_timeline:3,7Az:direct_inbox:4,7Az:direct_inbox:5,5rG:direct_thread:7",
                "is_ae_dual_send": "false",
                "offline_threading_id": timestamp,
                "text": formatted_message,
                "recipient_users": f"[[{user_id}]]"  # Note the double brackets for recipient_users
            }
            
            response = self.session.post(
                url,
                headers=self.headers,
                data=payload
            )
            
            # Save session after sending message
            self.save_session()
            
            return response.status_code == 200, response
        except Exception as e:
            return False, None

import mysql.connector

def get_credentials_from_db():
    try:
        conn = mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="instaadmin",
            port=3306
        )
        cursor = conn.cursor()
        cursor.execute("SELECT username, password, secretkey FROM users WHERE id = 34;")
        row = cursor.fetchone()
        
        if row:
            username, password, secretkey = row
            secretkey = secretkey.replace(" ", "")
            return username, password, secretkey
        else:
            raise Exception("No credentials found in database")
    except mysql.connector.Error as e:
        raise
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def load_recipients():
    try:
        df = pd.read_csv(r'C:\files\bio_messages_34.csv')
        df = df[df['title'] != "Instagram User"]
        df = df[df['title'] != "Instagram user"]
        # Now include the message column
        recipients = df[['id1', 'username', 'id3', 'message']].values.tolist()
        return recipients
    except Exception as e:
        return None

def get_last_processed_username(messages_file):
    try:
        if os.path.exists(messages_file):
            df = pd.read_csv(messages_file)
            if not df.empty:
                return df['username'].iloc[-1]
        return None
    except Exception as e:
        return None

def save_message_log(thread_id, username, status, messages_file):
    try:
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        data = {
            'id': [thread_id],
            'username': [username],
            'status': [status],
            'timestamp': [timestamp]
        }
        df = pd.DataFrame(data)
        if os.path.exists(messages_file):
            df.to_csv(messages_file, mode='a', header=False, index=False)
        else:
            df.to_csv(messages_file, index=False)
    except Exception as e:
        pass

def log_skipped_message(username, message, reason):
    """Log skipped messages to a text file"""
    try:
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_file = r'C:\files\skipped_messages_log.txt'
        
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"\n{'='*60}\n")
            f.write(f"Timestamp: {timestamp}\n")
            f.write(f"Username: {username}\n")
            f.write(f"Reason: {reason}\n")
            f.write(f"Message: {message[:200]}...\n" if len(message) > 200 else f"Message: {message}\n")
            f.write(f"{'='*60}\n")
    except:
        pass

def contains_error_patterns(message):
    """Check if message contains error-related patterns"""
    if not message or not isinstance(message, str):
        return True, "empty_or_invalid_message"
    
    # Convert to lowercase for case-insensitive matching
    message_lower = message.lower()
    
    # Define error patterns to check
    error_patterns = [
        # Error patterns
        'error', 'exception', 'failed', 'failure', 'fail',
        'invalid', 'unable', 'could not', 'cannot', 'can\'t',
        'unauthorized', 'forbidden', 'denied', 'rejected',
        
        # Rate limiting patterns
        'rate limit', 'too many', 'try again later', 'please wait',
        'temporarily blocked', 'spam', 'suspended', 'restricted',
        
        # Technical patterns
        'traceback', 'stack trace', 'debug', 'warning',
        'critical', 'fatal', 'abort', 'crash', 'timeout',
        
        # API/System patterns
        '404', '403', '401', '500', '429', 'http error',
        'connection error', 'network error', 'server error',
        'api error', 'request failed', 'response error',
        
        # Database/System messages
        'database error', 'mysql', 'sql error', 'query failed',
        'no data', 'not found', 'missing', 'null', 'undefined',
        
        # Instagram specific
        'instagram error', 'ig error', 'checkpoint', 'challenge required',
        'verification required', 'account suspended', 'action blocked'
    ]
    
    # Check each pattern
    for pattern in error_patterns:
        if pattern in message_lower:
            return True, f"contains_{pattern.replace(' ', '_')}"
    
    # Check for suspicious technical content
    if any(char in message for char in ['<', '>', '{', '}', 'SELECT', 'INSERT', 'UPDATE', 'DELETE']):
        return True, "suspicious_technical_content"
    
    # Check message length (too short might be an error)
    if len(message.strip()) < 5:
        return True, "message_too_short"
    
    return False, None


def main():
    try:
        if len(sys.argv) < 2 or sys.argv[1] not in ['start', 'resume']:
            print("Usage: python script.py [start|resume] [message_limit]")
            print("Example: python script.py start 1000")
            print("Default message limit: 470-550 (random)")
            return
            
        mode = sys.argv[1]
        
        # Get message limit from command line or use random default
        default_message_limit = random.randint(470, 550)
        message_limit = default_message_limit
        
        if len(sys.argv) >= 3:
            try:
                message_limit = int(sys.argv[2])
                if message_limit <= 0:
                    print("Message limit must be a positive number")
                    return
                print(f"Custom message limit set to: {message_limit}")
            except ValueError:
                print("Invalid message limit. Must be a number.")
                return
        else:
            print(f"Using default message limit: {message_limit}")
        messages_file = r'C:\files\first_message34.csv'
        print(f"\n=== Starting in {mode} mode with {message_limit} message limit ===")
        
        if mode == 'start' and os.path.exists(messages_file):
            try:
                os.remove(messages_file)
                print("Removed existing messages file")
            except Exception as e:
                print(f"Error removing file: {str(e)}")
                return

        end_time = datetime.now() + timedelta(minutes=165)
        print(f"Session will end at: {end_time}")
        
        print("\n=== API Initialization ===")
        api = InstagramAPI()
        
        # Session and API initialization
        if os.path.exists('instagram_34.pkl'):
            print("Found existing session file")
            if not api.load_session():
                print("Session load failed, getting new credentials")
                username, password, totp_secret = get_credentials_from_db()
                totp = pyotp.TOTP(totp_secret)
                verification_code = totp.now()
                
                api.post_launcher_sync()
                time.sleep(2)
                api.get_qe_sync()
                time.sleep(2)
                if not api.login(username, password, verification_code):
                    raise Exception("Login failed")
                time.sleep(2)
                api.get_reels_tray()
                time.sleep(2)
                api.get_feed_timeline()
                time.sleep(2)
            else:
                print("Session loaded successfully")
        else:
            print("No session file found, starting fresh login")
            username, password, totp_secret = get_credentials_from_db()
            totp = pyotp.TOTP(totp_secret)
            verification_code = totp.now()
            
            api.post_launcher_sync()
            time.sleep(2)
            api.get_qe_sync()
            time.sleep(2)
            if not api.login(username, password, verification_code):
                raise Exception("Login failed")
            print("Login successful")
            time.sleep(2)
            api.get_reels_tray()
            time.sleep(2)
            api.get_feed_timeline()
            time.sleep(2)
        
        print("\n=== Loading Recipients ===")
        df_recipients = pd.read_csv(r'C:\files\bio_messages_34.csv')
        total_initial = len(df_recipients)
        print(f"Initial total rows in CSV: {total_initial}")
        
        # Filter Instagram Users first
        print("=== Filtering Instagram Users ===")
        initial_count = len(df_recipients)
        df_recipients = df_recipients[df_recipients['title'] != "Instagram User"]
        df_recipients = df_recipients[df_recipients['title'] != "Instagram user"]
        filtered_count = len(df_recipients)
        print(f"Removed {initial_count - filtered_count} Instagram Users")
        print(f"Remaining valid recipients: {filtered_count}")
        
        # Handle resume mode
        total_sent = 0
        if mode == 'resume':
            print("\n=== Resume Mode Details ===")
            try:
                messages_df = pd.read_csv(messages_file)
                processed_usernames = set(messages_df['username'].tolist())
                total_sent = len(processed_usernames)
                print(f"Messages already sent: {total_sent}")
                
                # Remove processed usernames
                df_recipients = df_recipients[~df_recipients['username'].isin(processed_usernames)]
                print(f"Recipients remaining after removing processed: {len(df_recipients)}")
                df_recipients = df_recipients.reset_index(drop=True)
            except Exception as e:
                print(f"Error processing message history: {str(e)}")
                return

        # Prepare final recipient list with user_id (id3) and message included
        recipients = df_recipients[['id1', 'username', 'id3', 'message']].values.tolist()
        remaining_count = len(recipients)
        
        print(f"\n=== Final Statistics ===")
        print(f"Total entries in CSV: {total_initial}")
        print(f"Valid recipients (non-Instagram Users): {filtered_count}")
        if mode == 'resume':
            print(f"Messages already sent: {total_sent}")
        print(f"Recipients remaining to process: {remaining_count}")

        if remaining_count == 0:
            print("\nWARNING: No recipients to process!")
            return

        successful = 0
        failed = 0
        skipped = 0
        consecutive_failures = 0
        messages_sent = 0
        
        print("\n=== Starting Message Sending Loop ===")
        print(f"Beginning to process {remaining_count} recipients")
        print(f"Message limit for this session: {message_limit}")
        
        for thread_id, username, user_id, custom_message in recipients:
            # Critical time check
            if datetime.now() >= end_time:
                print("\nReached time limit, stopping...")
                break
                
            # Message limit check
            if messages_sent >= message_limit:
                print(f"\nReached message limit of {message_limit}, stopping...")
                break
                
            try:
                # Check for error patterns BEFORE processing
                has_error, error_reason = contains_error_patterns(custom_message)
                if has_error:
                    print(f"\nSkipping {username} - Message contains error pattern: {error_reason}")
                    log_skipped_message(username, custom_message, error_reason)
                    skipped += 1
                    continue
                
                print(f"\nProcessing: {username}")
                print(f"Original message: {custom_message[:50]}...")  # Show first 50 chars of message
                
                # Show the formatted message that will be sent
                formatted_msg = api.format_message_with_username(custom_message, username)
                print(f"Formatted message: {formatted_msg[:50]}...")
                
                # First try with thread_id
                success, response = api.send_direct_message(thread_id, username, custom_message)
                
                # If thread_id fails, try with user_id as fallback
                if not success and user_id:
                    print(f"Thread ID method failed, trying User ID method as fallback")
                    success, response = api.send_direct_message_by_user_id(user_id, username, custom_message)
                
                status = 'success' if success else 'failed'
                save_message_log(thread_id, username, status, messages_file)
                
                if success:
                    successful += 1
                    messages_sent += 1
                    consecutive_failures = 0
                    print(f"✓ Success - {messages_sent}/{message_limit} messages sent | {successful}/{remaining_count} recipients")
                else:
                    failed += 1
                    consecutive_failures += 1
                    print(f"✗ Failed - Message not sent")
                    print(f"Failed count: {failed}, Consecutive failures: {consecutive_failures}")
                    
                    # Stop if too many consecutive failures
                    if consecutive_failures >= 10:
                        print("\nStopping due to 10 consecutive failures")
                        break
                
                delay = random.uniform(2.5, 4.5)
                print(f"Waiting {delay:.2f} seconds...")
                time.sleep(delay)
                
                if (successful + failed) % random.randint(40, 60) == 0:
                    long_delay = random.uniform(10, 30)
                    print(f"Taking longer break of {long_delay:.2f} seconds...")
                    time.sleep(long_delay)
                    
            except Exception as e:
                print(f"Error processing {username}: {str(e)}")
                save_message_log(thread_id, username, 'error', messages_file)
                failed += 1
                consecutive_failures += 1
                
                # Stop if too many consecutive failures
                if consecutive_failures >= 10:
                    print("\nStopping due to 10 consecutive failures")
                    break
                    
                continue

        print("\n=== Final Results ===")
        print(f"Session Summary:")
        print(f"Total processed in this session: {successful + failed + skipped}")
        print(f"Messages sent this session: {messages_sent}/{message_limit}")
        print(f"Successful: {successful}")
        print(f"Failed: {failed}")
        print(f"Skipped (error patterns): {skipped}")
        if mode == 'resume':
            print(f"Total messages sent (including previous): {total_sent + successful}")
        
        if messages_sent >= message_limit:
            print(f"\n*** REACHED MESSAGE LIMIT OF {message_limit} ***")
            print("To continue, run the script again with 'resume' mode")
        
        print("=" * 50)

    except Exception as e:
        print(f"\nCritical error during execution: {str(e)}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        print("Full traceback:")
        print(traceback.format_exc())

if __name__ == "__main__":
    main()