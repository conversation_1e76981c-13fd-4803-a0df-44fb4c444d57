import requests
import json
import time
import pandas as pd
import random
import concurrent.futures
import logging
import os
from datetime import datetime


# Set up logging
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"instagram_scraper_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")


# Configure logging
logging.basicConfig(
   filename=log_file,
   level=logging.INFO,
   format='%(asctime)s - %(levelname)s - %(message)s',
   datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger()


# Proxy configuration
PROXY_HOST = "gw.netnut.net"
PROXY_PORT = "5959"
PROXY_USER_BASE = "sbinrimb25"
PROXY_PASSWORD = "djXH6LUGqqwBODa"


# List of countries to rotate through
COUNTRIES = ["US", "DE", "FR", "IN", "JP","ca","il","tw","ae"]
country_index = 0  # Global index to track which country to use next


# Batch size - number of profiles to fetch with each proxy in parallel
BATCH_SIZE = 20


# Maximum number of batches to process before stopping
MAX_BATCHES = 100


def get_proxy_url():
   """
   Return a formatted proxy URL for the next country in rotation
   """
   global country_index
   country_code = COUNTRIES[country_index]
   country_index = (country_index + 1) % len(COUNTRIES)  # Move to next country for next call
 
   proxy_user = f"{PROXY_USER_BASE}-res-{country_code}"
   proxy_url = f"http://{proxy_user}:{PROXY_PASSWORD}@{PROXY_HOST}:{PROXY_PORT}"
 
   logger.info(f"Using proxy from country: {country_code}")
   return proxy_url, country_code


def get_instagram_profile(args):
   """
   Get Instagram profile data using the provided proxy
   """
   username, proxy_url, id1, id3, title = args
   logger.debug(f"Fetching profile for: {username}")
 
   url = f"https://www.instagram.com/api/v1/users/web_profile_info/?username={username}"
 
   headers = {
       'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
       'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
       'Accept-Language': 'en-US,en;q=0.5',
       'X-IG-App-ID': '936619743392459',
       'Referer': 'https://www.instagram.com/',
       'Origin': 'https://www.instagram.com',
   }
 
   proxies = {
       "http": proxy_url,
       "https": proxy_url
   }
 
   try:
       response = requests.get(url, headers=headers, proxies=proxies, timeout=15)
     
       # Process successful response
       if response.status_code == 200:
           data = response.json()
           user_data = data.get('data', {}).get('user', {})
         
           if not user_data:
               logger.warning(f"No data found for {username}")
               return {
                   'username': username,
                   'id1': id1,
                   'id3': id3,
                   'title': title,
                   'bio': '',
                   'posts': None,
                   'followers': None,
                   'following': None
               }
         
           # We store id1, id3, and title from our input data
           result = {
               'username': username,
               'id1': id1,
               'id3': id3,
               'title': title,
               'bio': user_data.get('biography', ''),
               'posts': user_data.get('edge_owner_to_timeline_media', {}).get('count'),
               'followers': user_data.get('edge_followed_by', {}).get('count'),
               'following': user_data.get('edge_follow', {}).get('count')
           }
         
           logger.info(f"Profile retrieved: {username} | Followers: {result['followers']}")
           return result
       else:
           logger.error(f"HTTP Error: {response.status_code} for {username}")
           return {
               'username': username,
               'id1': id1,
               'id3': id3,
               'title': title,
               'bio': '',
               'posts': None,
               'followers': None,
               'following': None
           }
         
   except Exception as e:
       logger.error(f"Error for {username}: {str(e)}")
       return {
           'username': username,
           'id1': id1,
           'id3': id3,
           'title': title,
           'bio': '',
           'posts': None,
           'followers': None,
           'following': None
       }


def process_batch_parallel(username_id_pairs, proxy_url):
   """
   Process a batch of username, id1, id3, and title pairs in parallel using the same proxy
   """
   # Create arguments for each username with id1, id3, and title
   args = [(username, proxy_url, id1, id3, title) for username, id1, id3, title in username_id_pairs]
 
   # Process all profiles in parallel
   with concurrent.futures.ThreadPoolExecutor(max_workers=BATCH_SIZE) as executor:
       results = list(executor.map(get_instagram_profile, args))
 
   return results


def main():
   logger.info("Starting Instagram profile scraper")
 
   # File paths
   input_file = r'C:\files\account_data_2.csv'
   output_file = r'C:\files\instagram_profiles_data_2.csv'
   final_output_file = r'C:\files\bio_2.csv'
   progress_file = r'C:\files\bio_2_progress.json'
 
   logger.info(f"Input file: {input_file}")
   logger.info(f"Output files: {output_file}, {final_output_file}")
   logger.info(f"Progress file: {progress_file}")
 
   # Read the CSV file
   try:
       df = pd.read_csv(input_file)
       logger.info(f"Loaded CSV with {len(df)} rows")
     
   except Exception as e:
       logger.critical(f"Error reading CSV file: {str(e)}")
       return
 
   # Check required columns
   username_col = 'username'
   id1_col = 'id1'
   id3_col = 'id3'
   title_col = 'title'
 
   required_cols = [username_col, id1_col, id3_col, title_col]
   missing_cols = [col for col in required_cols if col not in df.columns]
 
   if missing_cols:
       logger.critical(f"Error: Columns {missing_cols} not found in CSV")
       logger.info(f"Available columns: {df.columns.tolist()}")
       return
 
   # Load progress if exists
   start_index = 0
   processed_usernames = set()
   batch_count = 0  # Always start with 0 batches for this run
   
   # Check if progress file exists
   if os.path.exists(progress_file):
       try:
           with open(progress_file, 'r') as f:
               progress_data = json.load(f)
               start_index = progress_data.get('last_index', 0)
               # Don't load batch_count - always start from 0
               processed_usernames = set(progress_data.get('processed_usernames', []))
               logger.info(f"Resuming from index {start_index}")
               logger.info(f"Already processed {len(processed_usernames)} usernames")
       except Exception as e:
           logger.warning(f"Could not load progress file: {e}")
           start_index = 0
   
   # Load existing results if available
   if os.path.exists(output_file):
       try:
           existing_df = pd.read_csv(output_file)
           # Add existing usernames to processed set
           if 'username' in existing_df.columns:
               existing_usernames = existing_df['username'].tolist()
               processed_usernames.update(existing_usernames)
               logger.info(f"Loaded {len(existing_df)} existing results")
           results_df = existing_df
       except Exception as e:
           logger.warning(f"Could not load existing output file: {e}")
           results_df = pd.DataFrame(columns=[
               'username', 'id1', 'id3', 'title', 'bio', 'posts', 'followers', 'following'
           ])
   else:
       # Create a DataFrame for results - now including id3 and title columns
       results_df = pd.DataFrame(columns=[
           'username', 'id1', 'id3', 'title', 'bio', 'posts', 'followers', 'following'
       ])
 
   # Get all usernames and their corresponding id1, id3, and title values
   # Filter out rows with missing usernames
   valid_data = df.dropna(subset=[username_col])
 
   # Create list of (username, id1, id3, title) quadruplets
   username_id_pairs = list(zip(valid_data[username_col], valid_data[id1_col], valid_data[id3_col], valid_data[title_col]))
 
   # Filter out already processed usernames
   username_id_pairs = [(u, i1, i3, t) for u, i1, i3, t in username_id_pairs if u not in processed_usernames]
   logger.info(f"Total usernames to process: {len(username_id_pairs)}")
   
   # Calculate how many batches we can process
   remaining_usernames = len(username_id_pairs) - start_index
   possible_batches = (remaining_usernames + BATCH_SIZE - 1) // BATCH_SIZE  # Round up
   batches_to_process = min(possible_batches, MAX_BATCHES)
   
   logger.info(f"This execution will process up to {batches_to_process} batches (max {MAX_BATCHES} per run)")
   logger.info(f"Starting from index: {start_index}")
 
   # Process usernames in batches, stopping after MAX_BATCHES
   last_processed_index = start_index
   for i in range(start_index, len(username_id_pairs), BATCH_SIZE):
       last_processed_index = i
       batch = username_id_pairs[i:i+BATCH_SIZE]
       batch_count += 1
     
       logger.info(f"Processing batch {batch_count}/{MAX_BATCHES} for this execution ({len(batch)} profiles in parallel)")
     
       # Get a proxy for this batch
       proxy_url, country_code = get_proxy_url()
     
       # Process the batch in parallel
       batch_results = process_batch_parallel(batch, proxy_url)
     
       # Add batch results to the results DataFrame
       for result in batch_results:
           results_df = pd.concat([results_df, pd.DataFrame([result])], ignore_index=True)
           processed_usernames.add(result['username'])
     
       # Save results after each batch
       results_df.to_csv(output_file, index=False)
       logger.info(f"Saved data for batch {batch_count}")
       
       # Save progress
       progress_data = {
           'last_index': i + BATCH_SIZE,
           'processed_usernames': list(processed_usernames)
       }
       with open(progress_file, 'w') as f:
           json.dump(progress_data, f)
       logger.info(f"Saved progress: index {i + BATCH_SIZE}, total processed: {len(processed_usernames)}")
     
       # Stop after processing MAX_BATCHES
       if batch_count >= MAX_BATCHES:
           logger.info(f"Reached maximum of {MAX_BATCHES} batches, stopping")
           break
     
       # Brief pause before the next batch with a new proxy
       if i + BATCH_SIZE < len(username_id_pairs):
           logger.info("Changing proxy for next batch...")
 
   # Process the final data to combine fields into bio
   if not results_df.empty:
       final_df = results_df.copy()
     
       # Combine fields into bio
       for index, row in final_df.iterrows():
           bio_text = row['bio'] if pd.notna(row['bio']) else ""
         
           # Format the stats directly in the bio as requested
           stats_text = ""
           if pd.notna(row['posts']) and pd.notna(row['followers']) and pd.notna(row['following']):
               stats_text = f"\nposts{row['posts']}followers{row['followers']}following{row['following']}"
         
           # Append stats to bio
           final_df.at[index, 'bio'] = bio_text + stats_text
     
       # Keep only the columns we want in the final output, now including id3 and title
       final_df = final_df[['username', 'id1', 'id3', 'title', 'bio']]
       final_df.to_csv(final_output_file, index=False)
     
       logger.info(f"Raw data saved to: {output_file}")
       logger.info(f"Final processed data saved to: {final_output_file}")
       logger.info(f"Processed {len(results_df)} profiles across {batch_count} batches")
 
   logger.info(f"Instagram profile scraper completed - Processed {batch_count} batches in this execution")
   
   # Check if all usernames have been processed
   if last_processed_index >= len(username_id_pairs) - BATCH_SIZE:
       logger.info("All usernames have been processed!")
       # Automatic cleanup when all data is done
       if os.path.exists(progress_file):
           os.remove(progress_file)
           logger.info("Progress file removed - next run will start from beginning")


if __name__ == "__main__":
   main()