import requests
import logging
import csv
import time
import os
import threading
import pandas as pd
import io
from datetime import datetime, timedelta
from threading import Lock, Thread
from queue import Queue


# Configure logging with UTF-8 encoding to handle emojis and special characters
log_file = io.open("api_extractor.log", "a", encoding="utf-8")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(log_file),  # Use StreamHandler with UTF-8 file
        logging.StreamHandler(),  # Also log to console
    ]
)
logger = logging.getLogger()


# API Configuration
BASE_URL = "https://api.allinonepride.com/api/v1"
API_KEY = "allinone_EAAKb6FyhhiwBOxE2DRZB6c3zKJZCafSOH1lADx6ZByZCF5Y2Mk5r7rn4CjE8FEfIzG9ZA3sNPIuaetDS00eNABSik8ZC5WY4JEoLwwpFSXNEOcTsEOLMB1Uev4uDvkBtbVRoWz0fLJwrFRl8j65R1f8ZAerGMmykeENPJgBjlKiFNgT0yAzCA3c4X1Wkv3LdIx32wZDZD"
USER_EMAIL = "<EMAIL>"


# Headers for API requests
HEADERS = {
    "X-Api-Key": API_KEY,
    "Accept": "application/json",
    "Content-Type": "application/json"
}


# Output directory and CSV files
OUTPUT_DIR = r"C:\files"
MAIN_CSV = os.path.join(OUTPUT_DIR, "failed_messages.csv")
SUCCESS_CSV = os.path.join(OUTPUT_DIR, "success_messages.csv")
ERROR_CSV = os.path.join(OUTPUT_DIR, "error_messages.csv")
DELETED_CSV = os.path.join(OUTPUT_DIR, "deleted_messages.csv")


# Create output directory if it doesn't exist
os.makedirs(OUTPUT_DIR, exist_ok=True)


# Interval in seconds for periodic execution
INTERVAL = 10


# Threshold for deleting old messages (510 minutes = 8.5 hours)
OLD_MESSAGE_THRESHOLD_MINUTES = 510


# Create locks for thread safety
csv_lock = Lock()
api_lock = Lock()


# Queue for messages to be deleted from API
delete_queue = Queue()


# Define status constants
STATUS_PENDING = "pending"
STATUS_SENT = "sent"
STATUS_SEND_FAILED = "send_failed"
STATUS_SESSION_NOT_FOUND = "session_not_found"
STATUS_USER_ID_NOT_FOUND = "user_id_not_found"
STATUS_USER_NOT_FOUND = "user_not_found"
STATUS_LOGIN_FAILED = "login_failed"
STATUS_LOGIN_ERROR = "login_error"
STATUS_ERROR = "error"
STATUS_DELETED = "deleted"
STATUS_QUEUED_FOR_DELETION = "queued_for_deletion"
STATUS_TOO_OLD = "too_old_not_sent"


# Lists of status categories
ERROR_STATUSES = [
    STATUS_SEND_FAILED,
    STATUS_SESSION_NOT_FOUND,
    STATUS_USER_ID_NOT_FOUND,
    STATUS_USER_NOT_FOUND,
    STATUS_LOGIN_FAILED,
    STATUS_LOGIN_ERROR,
    STATUS_ERROR,
    STATUS_TOO_OLD
]


# Only "sent" messages are deletable
DELETABLE_STATUSES = [STATUS_SENT]


def get_data_from_api(email, next_cursor=None):
    """
    Retrieve data from the API for a specific user email with pagination support
    """
    endpoint = f"{BASE_URL}/failed-messages/{email}"
    params = {
        'page[size]': 100  # Set maximum page size to 100
    }
   
    if next_cursor:
        params['page[cursor]'] = next_cursor
   
    try:
        with api_lock:
            response = requests.get(endpoint, headers=HEADERS, params=params)
       
        # Check if the request was successful
        if response.status_code == 200:
            data = response.json()
            message_count = len(data.get("data", []))
            logger.info(f"Retrieved {message_count} messages from API")
            return data
        else:
            logger.error(f"Error retrieving data: {response.status_code} - {response.text}")
            return None
           
    except Exception as e:
        logger.error(f"Exception occurred while retrieving data: {str(e)}")
        return None


def delete_failed_messages(message_ids):
    """
    Delete processed messages from the API
    """
    if not message_ids:
        logger.info("No messages to delete")
        return True
   
    endpoint = f"{BASE_URL}/failed-messages-delete"
    payload = {"message_ids": message_ids}
   
    try:
        with api_lock:
            response = requests.post(endpoint, headers=HEADERS, json=payload)
       
        if response.status_code == 200:
            logger.info(f"Successfully deleted {len(message_ids)} messages from API")
            return True
        else:
            logger.error(f"Error deleting messages: {response.status_code} - {response.text}")
            return False
           
    except Exception as e:
        logger.error(f"Exception occurred while deleting messages: {str(e)}")
        return False


def extract_specific_fields(data):
    """
    Extract only the specific fields we need:
    - message_id (new field)
    - username (formerly platform_username)
    - business_client_username
    - message
    - created_at
    - updated_at
    - status (new field, default "pending" or "too_old_not_sent" based on age)
    - deletion_reason (track why a message was deleted)
    - previous_status (track status changes)
    - deleted_at (timestamp when message was deleted)
    """
    extracted_records = []
    current_time = datetime.now()
   
    for message in data.get("data", []):
        attributes = message.get("attributes", {})
        conversation = attributes.get("conversation", {})
        business_client = conversation.get("business_client", {})
        business_account = conversation.get("business_account", {})
        
        created_at_str = attributes.get("created_at", "")
        
        # Determine status based on message age
        status = STATUS_PENDING  # Default status
        
        if created_at_str:
            try:
                # Parse the created_at timestamp (assuming ISO format)
                # Handle different possible timestamp formats
                if 'T' in created_at_str:
                    # ISO format with T separator
                    if created_at_str.endswith('Z'):
                        # Handle microseconds if present
                        if '.' in created_at_str:
                            # Format: 2025-05-22T14:06:37.000000Z
                            created_at = datetime.strptime(created_at_str, "%Y-%m-%dT%H:%M:%S.%fZ")
                        else:
                            # Format: 2025-05-22T14:06:37Z
                            created_at = datetime.strptime(created_at_str, "%Y-%m-%dT%H:%M:%SZ")
                    elif '+' in created_at_str or created_at_str.count(':') > 2:
                        # ISO format with timezone
                        created_at = datetime.fromisoformat(created_at_str.replace('Z', '+00:00'))
                    else:
                        # ISO format without timezone
                        created_at = datetime.fromisoformat(created_at_str)
                else:
                    # Standard format
                    created_at = datetime.strptime(created_at_str, "%Y-%m-%d %H:%M:%S")
                
                # Calculate age in minutes
                age_minutes = (current_time - created_at).total_seconds() / 60
                
                # Set status based on age threshold
                if age_minutes > OLD_MESSAGE_THRESHOLD_MINUTES:
                    status = STATUS_TOO_OLD
                    logger.info(f"Message ID {message.get('id', 'unknown')} is {age_minutes:.1f} minutes old (>{OLD_MESSAGE_THRESHOLD_MINUTES} minutes) - marking as too old")
                else:
                    status = STATUS_PENDING
                    
            except Exception as e:
                logger.warning(f"Could not parse created_at timestamp '{created_at_str}': {str(e)} - defaulting to pending status")
                status = STATUS_PENDING
       
        record = {
            "message_id": message.get("id", ""),
            "username": business_account.get("platform_username", ""),
            "business_client_username": business_client.get("username", ""),
            "message": attributes.get("message", ""),
            "created_at": created_at_str,
            "updated_at": attributes.get("updated_at", ""),
            "status": status,
            "deletion_reason": "",
            "previous_status": "",
            "deleted_at": ""
        }
       
        # Log some debug info about extracted fields
        if not record["username"] or not record["business_client_username"]:
            logger.info(f"Incomplete data for message_id {record['message_id']}: " +
                      f"username={record['username']}, " +
                      f"business_client_username={record['business_client_username']}")
       
        extracted_records.append(record)
   
    logger.info(f"Extracted {len(extracted_records)} records from API data")
    return extracted_records


def read_csv_to_dataframe(csv_file):
    """
    Read a CSV file into a pandas DataFrame, creating an empty one if the file doesn't exist or is empty
    """
    # All required columns that should be in every CSV
    required_columns = [
        "message_id", "username", "business_client_username", "message",
        "created_at", "updated_at", "status", "deletion_reason", "previous_status",
        "deleted_at"
    ]
   
    try:
        if os.path.exists(csv_file):
            # Check if file is empty
            if os.path.getsize(csv_file) == 0:
                logger.info(f"File {os.path.basename(csv_file)} exists but is empty, creating with default columns")
                df = pd.DataFrame(columns=required_columns)
                # Save properly formatted CSV
                df.to_csv(csv_file, index=False)
                return df
           
            try:
                # Try to read the file
                df = pd.read_csv(csv_file)
               
                # Add any missing columns
                for col in required_columns:
                    if col not in df.columns:
                        df[col] = ""
                        logger.info(f"Added missing column '{col}' to {os.path.basename(csv_file)}")
               
                return df
            except pd.errors.EmptyDataError:
                # Handle empty file case
                logger.info(f"File {os.path.basename(csv_file)} contains no data, creating with default columns")
                df = pd.DataFrame(columns=required_columns)
                # Save properly formatted CSV
                df.to_csv(csv_file, index=False)
                return df
        else:
            # Create an empty DataFrame with the required columns
            logger.info(f"Creating new DataFrame for {os.path.basename(csv_file)}")
            return pd.DataFrame(columns=required_columns)
    except Exception as e:
        logger.error(f"Error reading CSV file {csv_file}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
       
        # Return an empty DataFrame
        return pd.DataFrame(columns=required_columns)


def check_for_status_changes():
    """
    Check for status changes in the main CSV and move records to appropriate files
    Only delete messages with "sent" status, preserve all others
    """
    if not os.path.exists(MAIN_CSV):
        logger.info(f"No existing main CSV file found at {MAIN_CSV}")
        return
   
    try:
        with csv_lock:
            # Read all CSV files
            main_df = read_csv_to_dataframe(MAIN_CSV)
            success_df = read_csv_to_dataframe(SUCCESS_CSV)
            error_df = read_csv_to_dataframe(ERROR_CSV)
           
            if main_df.empty:
                return
           
            # MODIFIED: Find messages with only the "sent" status for deletion
            deletable_messages = main_df[main_df['status'].isin(DELETABLE_STATUSES)]
           
            if deletable_messages.empty:
                return
           
            # Process each deletable message
            for index, row in deletable_messages.iterrows():
                message_id = row['message_id']
                current_status = row['status']
               
                # Skip if no message_id
                if not message_id:
                    continue
               
                # Add to delete queue with detailed information
                delete_queue.put((current_status, message_id, index))
                logger.info(f"Queued message for deletion - ID: {message_id}, Status: {current_status}")
               
                # Update status in dataframe
                main_df.at[index, 'previous_status'] = current_status
                main_df.at[index, 'status'] = STATUS_QUEUED_FOR_DELETION
                main_df.at[index, 'deletion_reason'] = current_status
               
                # Copy to success CSV
                row_copy = row.copy()
                row_copy['previous_status'] = current_status
                row_copy['status'] = STATUS_QUEUED_FOR_DELETION
                success_df = pd.concat([success_df, pd.DataFrame([row_copy])], ignore_index=True)
           
            # MODIFIED: Process error status messages - copy to error CSV but don't delete
            error_messages = main_df[main_df['status'].isin(ERROR_STATUSES)]
            for index, row in error_messages.iterrows():
                row_copy = row.copy()
                error_df = pd.concat([error_df, pd.DataFrame([row_copy])], ignore_index=True)
                logger.info(f"Copied error message to error CSV - ID: {row['message_id']}, Status: {row['status']}")
           
            # Save updated DataFrames
            main_df.to_csv(MAIN_CSV, index=False)
            success_df.to_csv(SUCCESS_CSV, index=False)
            error_df.to_csv(ERROR_CSV, index=False)
           
            logger.info(f"Updated status for {len(deletable_messages)} messages to '{STATUS_QUEUED_FOR_DELETION}'")
            logger.info(f"Copied {len(error_messages)} error messages to error CSV (not deleting)")
   
    except Exception as e:
        logger.error(f"Error checking for status changes: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())


def delete_old_messages():
    """
    Check for messages with "sent" status in CSV and mark them for deletion
    MODIFIED: All messages with "sent" status should be deleted, regardless of age
    Other status messages are never deleted
    """
    if not os.path.exists(MAIN_CSV):
        logger.info(f"No existing main CSV file found at {MAIN_CSV}")
        return
   
    try:
        # Read the CSV into a pandas DataFrame with lock
        with csv_lock:
            main_df = read_csv_to_dataframe(MAIN_CSV)
           
            if main_df.empty:
                logger.info("Main CSV file is empty, no messages to check")
                return
               
            # Get only messages with "sent" status that aren't already queued for deletion
            deletable_messages = main_df[(main_df['status'] == STATUS_SENT) & 
                                        (main_df['status'] != STATUS_QUEUED_FOR_DELETION)]
           
            if deletable_messages.empty:
                return
           
            rows_to_update = []
           
            for index, row in deletable_messages.iterrows():
                if row['message_id']:
                    # Add to delete queue for sent messages only
                    deletion_reason = "sent_status"
                    delete_queue.put((row['status'], row['message_id'], index, deletion_reason))
                   
                    # Update status in the DataFrame
                    main_df.at[index, 'previous_status'] = row['status']
                    main_df.at[index, 'status'] = STATUS_QUEUED_FOR_DELETION
                    main_df.at[index, 'deletion_reason'] = deletion_reason
                   
                    rows_to_update.append(index)
               
                # Log message with emoji handling
                try:
                    logger.info(f"Found message with sent status - ID: {row['message_id']}, "
                               f"Username: {row['username']}, "
                               f"Client: {row['business_client_username']}")
                except UnicodeEncodeError:
                    # Fallback logging without the message content that may contain emojis
                    logger.info(f"Found message with sent status - ID: {row['message_id']}")
           
            # Save updated DataFrame back to CSV
            if rows_to_update:
                main_df.to_csv(MAIN_CSV, index=False)
                logger.info(f"Updated status to '{STATUS_QUEUED_FOR_DELETION}' for {len(rows_to_update)} messages with sent status in CSV")
   
    except Exception as e:
        logger.error(f"Error handling messages in CSV: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())


def check_api_for_old_messages():
    """
    Check API for messages that might not be in the CSV
    MODIFIED: Only delete messages that have "sent" status, regardless of age
    Never delete messages with other statuses
    """
    try:
        # Get all messages from API
        all_api_records = []
        next_cursor = None
       
        while True:
            data = get_data_from_api(USER_EMAIL, next_cursor)
            if not data or not data.get("data"):
                if not data:
                    logger.info("No data received from API when checking for messages")
                else:
                    logger.info("No messages found in API when checking")
                break
               
            api_records = extract_specific_fields(data)
            all_api_records.extend(api_records)
           
            next_cursor = data.get("meta", {}).get("next_cursor")
            if not next_cursor:
                break
       
        if all_api_records:
            current_time = datetime.now()
            deleted_count = 0
            message_ids_to_delete = []
           
            for record in all_api_records:
                # Delete only records with "sent" status
                if record['status'] == STATUS_SENT:
                    if record['message_id']:
                        # Add to direct deletion list
                        message_ids_to_delete.append(record['message_id'])
                       
                        # Add to deleted CSV
                        record['previous_status'] = record['status']
                        record['status'] = STATUS_DELETED
                        record['deletion_reason'] = "api_sent_status"
                        record['deleted_at'] = current_time.isoformat()
                       
                        with csv_lock:
                            deleted_df = read_csv_to_dataframe(DELETED_CSV)
                            deleted_df = pd.concat([deleted_df, pd.DataFrame([record])], ignore_index=True)
                            deleted_df.to_csv(DELETED_CSV, index=False)
                       
                        deleted_count += 1
                        logger.info(f"Found sent record in API - ID: {record['message_id']}")
           
            # Delete the sent messages directly from API
            if message_ids_to_delete:
                success = delete_failed_messages(message_ids_to_delete)
                if success:
                    logger.info(f"Successfully deleted {deleted_count} sent messages directly from API")
                else:
                    logger.error(f"Failed to delete {deleted_count} sent messages from API")
           
        else:
            logger.info("No API records found to check")
   
    except Exception as e:
        logger.error(f"Error checking API for messages: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())


def read_existing_records():
    """
    Read existing records from CSV file to check for duplicates
    Returns a dictionary mapping record keys to their current status
    Format: {(username, business_client_username, message): status}
    """
    existing_records = {}
   
    if not os.path.exists(MAIN_CSV):
        logger.info(f"No existing main CSV file found at {MAIN_CSV}")
        return existing_records
   
    try:
        with csv_lock:
            try:
                # First check if file exists and is not empty
                if os.path.getsize(MAIN_CSV) == 0:
                    logger.info(f"Main CSV file is empty, no existing records to load")
                    return existing_records
               
                df = pd.read_csv(MAIN_CSV)
                if df.empty:
                    return existing_records
                   
                # Extract records from dataframe
                for _, row in df.iterrows():
                    # Extract and normalize the key fields
                    username = str(row.get('username', '')).strip()
                    business_client_username = str(row.get('business_client_username', '')).strip()
                    message = str(row.get('message', '')).strip()
                    status = str(row.get('status', '')).strip()
                   
                    # Only add to existing records if all three fields have values
                    if username and business_client_username and message:
                        # Normalize the values to prevent issues with case and whitespace
                        record_key = (
                            username.lower(),
                            business_client_username.lower(),
                            message.lower()
                        )
                        existing_records[record_key] = status
                       
            except pd.errors.EmptyDataError:
                logger.info(f"CSV file is empty or invalid format, no existing records to load")
                return existing_records
           
            logger.info(f"Loaded {len(existing_records)} valid existing records from CSV")
            return existing_records
   
    except Exception as e:
        logger.error(f"Error reading existing CSV: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return set()


def filter_duplicates(records, existing_records):
    """
    Filter out duplicate records based on username, business_client_username, and message
    FIXED: This function ONLY prevents adding duplicate records to CSV.
    It NEVER deletes existing records - deletion is handled by other functions based on status.
    
    Logic:
    - If record is new: Add it to CSV
    - If record is duplicate: Skip adding it (preserve existing record)
    - NEVER delete anything based on age or status here
    """
    filtered_records = []
    duplicates_count = 0
   
    for record in records:
        # Extract and normalize the fields
        username = str(record.get('username', '')).strip()
        business_client_username = str(record.get('business_client_username', '')).strip()
        message = str(record.get('message', '')).strip()
       
        # Skip records with missing values in any of the three key fields
        if not username or not business_client_username or not message:
            # Still add records with missing fields to the filtered list
            # but don't check them for duplicates
            filtered_records.append(record)
            logger.debug(f"Skipping duplicate check for incomplete record with ID: {record.get('message_id', 'unknown')}")
            continue
       
        # Create normalized record key for comparison
        record_key = (
            username.lower(),
            business_client_username.lower(),
            message.lower()
        )
       
        if record_key not in existing_records:
            filtered_records.append(record)
            existing_records[record_key] = record.get('status', STATUS_PENDING)  # Add to existing records to prevent duplicates within batch
        else:
            duplicates_count += 1
            existing_status = existing_records[record_key]
            
            logger.info(f"Found duplicate message - ID: {record.get('message_id', 'unknown')}, "
                       f"Username: {username}, "
                       f"Client: {business_client_username}, "
                       f"Existing Status: {existing_status}")
           
            # FIXED: For duplicates, simply skip the new record and preserve the existing one
            # Do NOT delete anything - duplicate checking should only prevent adding new duplicates
            # Deletion based on status or age is handled by other functions
            logger.info(f"Skipping duplicate message (existing status: {existing_status}) - ID: {record.get('message_id', 'unknown')}")
   
    if duplicates_count > 0:
        logger.info(f"Processed {duplicates_count} duplicate records")
   
    return filtered_records


def save_to_csv(records, csv_file, existing_file=True):
    """
    Save the extracted records to CSV format
    """
    if not records:
        return
       
    try:
        with csv_lock:
            # Make sure the columns include all necessary fields
            required_columns = [
                "message_id", "username", "business_client_username", "message",
                "created_at", "updated_at", "status", "deletion_reason", "previous_status",
                "deleted_at"
            ]
           
            # Ensure all records have the required columns
            for record in records:
                for col in required_columns:
                    if col not in record:
                        record[col] = ""
           
            # Determine if file exists to decide on mode and header
            file_exists = os.path.exists(csv_file) and existing_file
           
            if file_exists:
                # If file exists but is empty, create it properly
                if os.path.getsize(csv_file) == 0:
                    # Create new DataFrame
                    df = pd.DataFrame(records)
                    # Ensure all required columns exist
                    for col in required_columns:
                        if col not in df.columns:
                            df[col] = ""
                    df.to_csv(csv_file, index=False)
                    logger.info(f"Created new CSV with {len(records)} records at {csv_file}")
                    return
               
                try:
                    # If file exists, read it first
                    df = pd.read_csv(csv_file)
                   
                    # Convert records to DataFrame
                    records_df = pd.DataFrame(records)
                   
                    # Append new records
                    df = pd.concat([df, records_df], ignore_index=True)
                   
                    # Ensure all required columns exist
                    for col in required_columns:
                        if col not in df.columns:
                            df[col] = ""
                   
                    # Save to CSV
                    df.to_csv(csv_file, index=False)
                    logger.info(f"Successfully saved {len(records)} records to {csv_file}")
                   
                except pd.errors.EmptyDataError:
                    # Handle case where file exists but is not properly formatted
                    df = pd.DataFrame(records)
                    # Ensure all required columns exist
                    for col in required_columns:
                        if col not in df.columns:
                            df[col] = ""
                    df.to_csv(csv_file, index=False)
                    logger.info(f"Created new CSV with {len(records)} records at {csv_file} (replaced malformed file)")
            else:
                # Create new DataFrame
                df = pd.DataFrame(records)
               
                # Ensure all required columns exist
                for col in required_columns:
                    if col not in df.columns:
                        df[col] = ""
               
                # Create directory if needed
                os.makedirs(os.path.dirname(csv_file), exist_ok=True)
               
                # Save to CSV
                df.to_csv(csv_file, index=False)
                logger.info(f"Created new CSV with {len(records)} records at {csv_file}")
       
    except Exception as e:
        logger.error(f"Error saving to CSV: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())


def deletion_worker():
    """
    Worker thread that processes the deletion queue and updates CSV after successful deletion
    """
    logger.info("Starting deletion worker thread")
   
    batch_size = 20  # Process up to 20 message IDs at once
    batch_timeout = 3  # Wait up to 3 seconds to fill a batch
   
    while True:
        try:
            # Collect a batch of message IDs to delete
            batch_ids = []
            batch_info = []  # Store more info for each ID (type, index, reason)
           
            # Get the first item with a timeout
            try:
                item = delete_queue.get(timeout=batch_timeout)
                if len(item) >= 2:  # Validate we have at least type and ID
                    msg_type, msg_id = item[0], item[1]
                    batch_ids.append(msg_id)
                    batch_info.append(item)
            except Exception:
                # Queue empty or timeout, continue to next iteration
                continue
           
            # Try to get more items without blocking
            start_time = time.time()
            while len(batch_ids) < batch_size and time.time() - start_time < batch_timeout:
                try:
                    item = delete_queue.get_nowait()
                    if len(item) >= 2:  # Validate we have at least type and ID
                        msg_type, msg_id = item[0], item[1]
                        batch_ids.append(msg_id)
                        batch_info.append(item)
                except Exception:
                    # Queue empty, wait a bit
                    time.sleep(0.1)
           
            # Delete the batch
            if batch_ids:
                reasons = ", ".join(set(item[0] for item in batch_info))
                logger.info(f"Deleting batch of {len(batch_ids)} messages (reasons: {reasons})")
               
                # Attempt to delete from API
                success = delete_failed_messages(batch_ids)
               
                if success:
                    # After successful deletion, update the CSVs
                    with csv_lock:
                        # Load all necessary dataframes
                        main_df = read_csv_to_dataframe(MAIN_CSV)
                        deleted_df = read_csv_to_dataframe(DELETED_CSV)
                       
                        for item in batch_info:
                            msg_type = item[0]
                            msg_id = item[1]
                           
                            # Find the row in main CSV
                            if not main_df.empty:
                                rows = main_df[main_df['message_id'] == msg_id]
                               
                                if not rows.empty:
                                    # Get the row data
                                    row_data = rows.iloc[0].to_dict()
                                   
                                    # Update status and other fields
                                    row_data['status'] = STATUS_DELETED
                                    row_data['previous_status'] = row_data.get('previous_status', msg_type)
                                   
                                    # Add detailed deletion reason
                                    if len(item) > 3 and item[3]:
                                        row_data['deletion_reason'] = item[3]
                                    else:
                                        row_data['deletion_reason'] = msg_type
                                   
                                    # Add timestamp when it was deleted
                                    row_data['deleted_at'] = datetime.now().isoformat()
                                   
                                    # Add to deleted CSV using pandas concat
                                    deleted_df = pd.concat([deleted_df, pd.DataFrame([row_data])], ignore_index=True)
                                   
                                    # Remove from main CSV
                                    main_df = main_df[main_df['message_id'] != msg_id]
                                   
                                    logger.info(f"Message ID {msg_id} moved to deleted messages CSV with reason: {row_data['deletion_reason']}")
                       
                        # Save the updated dataframes
                        main_df.to_csv(MAIN_CSV, index=False)
                        deleted_df.to_csv(DELETED_CSV, index=False)
                        logger.info(f"Successfully updated CSVs after deleting {len(batch_ids)} messages")
                else:
                    logger.error(f"Failed to delete {len(batch_ids)} messages from API")
               
                # Mark tasks as done
                for _ in range(len(batch_ids)):
                    delete_queue.task_done()
       
        except Exception as e:
            logger.error(f"Error in deletion worker: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            time.sleep(1)  # Avoid tight loop on errors


def csv_monitor_worker():
    """
    Worker thread that monitors the CSV file for status changes
    """
    logger.info("Starting CSV monitor thread")
   
    while True:
        try:
            # Check for status changes
            check_for_status_changes()
            time.sleep(2)  # Check every 2 seconds
        except Exception as e:
            logger.error(f"Error in CSV monitor: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            time.sleep(1)  # Avoid tight loop on errors


def old_message_checker_worker():
    """
    Worker thread that periodically checks for old messages
    """
    logger.info("Starting old message checker thread")
   
    while True:
        try:
            # Check for old messages in CSV
            delete_old_messages()
           
            # Check for old messages in API
            check_api_for_old_messages()
           
            # Wait before next check
            time.sleep(60)  # Check every minute
        except Exception as e:
            logger.error(f"Error in old message checker: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            time.sleep(5)  # Avoid tight loop on errors


def run_extraction():
    """
    Single run of the extraction process
    """
    logger.info("Starting API Data Extraction")
   
    total_records = 0
    new_records_added = 0
    next_cursor = None
   
    try:
        # Read existing records for duplicate checking
        existing_records = read_existing_records()
       
        # Continue fetching pages until there are no more
        while True:
            # Get data from the API
            data = get_data_from_api(USER_EMAIL, next_cursor)
           
            if not data:
                logger.info("No response received from API")
                break
               
            if not data.get("data"):
                logger.info("No data to retrieve from API")
                break
               
            # Extract the specific fields we need
            records = extract_specific_fields(data)
           
            # Filter out duplicates
            filtered_records = filter_duplicates(records, existing_records)
           
            # Save to main CSV if there are new records
            if filtered_records:
                save_to_csv(filtered_records, MAIN_CSV, os.path.exists(MAIN_CSV))
                new_records_added += len(filtered_records)
           
            total_records += len(records)
           
            # Check if there's a next page
            next_cursor = data.get("meta", {}).get("next_cursor")
            if not next_cursor:
                logger.info("No more pages to retrieve")
                break
               
            logger.info(f"Moving to next page with cursor: {next_cursor}")
       
        # Display a summary
        logger.info(f"Total messages processed: {total_records}")
        logger.info(f"New records added: {new_records_added}")
        if total_records == 0:
            logger.info("No messages found in API to process")
       
    except Exception as e:
        logger.error(f"Error in extraction process: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
   
    logger.info("Extraction completed")


def extraction_worker():
    """
    Worker thread that runs the extraction on a schedule
    """
    logger.info("Starting extraction worker thread")
   
    while True:
        try:
            # Run the extraction
            run_extraction()
           
            # Wait for the next interval
            logger.info(f"Waiting {INTERVAL} seconds until next extraction...")
            time.sleep(INTERVAL)
           
        except Exception as e:
            logger.error(f"Error in scheduled extraction: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            # Wait and try again
            time.sleep(INTERVAL)


def initialize_csv_files():
    """Initialize all CSV files with proper headers if they don't exist or are empty"""
    # All required columns that should be in every CSV
    required_columns = [
        "message_id", "username", "business_client_username", "message",
        "created_at", "updated_at", "status", "deletion_reason", "previous_status",
        "deleted_at"
    ]
   
    # Create necessary files if they don't exist
    for csv_file in [MAIN_CSV, SUCCESS_CSV, ERROR_CSV, DELETED_CSV]:
        try:
            file_exists = os.path.exists(csv_file)
           
            if not file_exists:
                # Create directory if needed
                os.makedirs(os.path.dirname(csv_file), exist_ok=True)
                # Create empty CSV with headers using pandas
                df = pd.DataFrame(columns=required_columns)
                df.to_csv(csv_file, index=False)
                logger.info(f"Created empty CSV file: {csv_file}")
            elif file_exists and os.path.getsize(csv_file) == 0:
                # File exists but is empty, add proper headers
                df = pd.DataFrame(columns=required_columns)
                df.to_csv(csv_file, index=False)
                logger.info(f"Added headers to empty CSV file: {csv_file}")
            else:
                # File exists and has content, check if it has all required columns
                try:
                    df = pd.read_csv(csv_file)
                    missing_columns = [col for col in required_columns if col not in df.columns]
                   
                    if missing_columns:
                        # Add missing columns
                        for col in missing_columns:
                            df[col] = ""
                        df.to_csv(csv_file, index=False)
                        logger.info(f"Added missing columns {missing_columns} to {csv_file}")
                except Exception as e:
                    # File exists but might be corrupted, recreate it
                    logger.warning(f"Error reading {csv_file}, recreating with proper format: {str(e)}")
                    df = pd.DataFrame(columns=required_columns)
                    df.to_csv(csv_file, index=False)
        except Exception as e:
            logger.error(f"Error initializing {csv_file}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())


def main():
    """
    Main function to start all worker threads
    """
    logger.info("===== Starting API data extraction system =====")
    logger.info(f"Main CSV: {MAIN_CSV}")
    logger.info(f"Success CSV: {SUCCESS_CSV}")
    logger.info(f"Error CSV: {ERROR_CSV}")
    logger.info(f"Deleted CSV: {DELETED_CSV}")
    logger.info(f"Running extraction every {INTERVAL} seconds")
    logger.info(f"MODIFIED BEHAVIOR: Only messages with 'sent' status will be deleted")
    logger.info(f"MODIFIED BEHAVIOR: Failed messages will be preserved for troubleshooting")
    logger.info(f"MODIFIED BEHAVIOR: Pending duplicates only deleted if older than 60 minutes")
   
    try:
        # Create OUTPUT_DIR if it doesn't exist
        os.makedirs(OUTPUT_DIR, exist_ok=True)
       
        # Initialize all CSV files
        initialize_csv_files()
       
        # Log all supported status types for clarity
        logger.info("Supported status types:")
        logger.info(f"  - {STATUS_PENDING}: Messages waiting to be processed (WILL BE PRESERVED)")
        logger.info(f"  - {STATUS_SENT}: Successfully sent messages (WILL BE DELETED IMMEDIATELY)")
        logger.info(f"  - {STATUS_SEND_FAILED}: Failed to send due to API error (WILL BE PRESERVED)")
        logger.info(f"  - {STATUS_SESSION_NOT_FOUND}: Session file not found (WILL BE PRESERVED)")
        logger.info(f"  - {STATUS_USER_ID_NOT_FOUND}: User ID not found in Instagram (WILL BE PRESERVED)")
        logger.info(f"  - {STATUS_USER_NOT_FOUND}: User not found in database (WILL BE PRESERVED)")
        logger.info(f"  - {STATUS_LOGIN_FAILED}: Authentication failed (WILL BE PRESERVED)")
        logger.info(f"  - {STATUS_LOGIN_ERROR}: Error during login process (WILL BE PRESERVED)")
        logger.info(f"  - {STATUS_ERROR}: General errors (WILL BE PRESERVED)")
        logger.info(f"  - {STATUS_TOO_OLD}: Messages older than {OLD_MESSAGE_THRESHOLD_MINUTES} minutes (WILL BE PRESERVED BUT NOT SENT)")
        logger.info(f"  - {STATUS_DELETED}: Message has been deleted from API")
        logger.info(f"  - {STATUS_QUEUED_FOR_DELETION}: Message is queued for deletion")
       
        # Perform immediate cleanup of sent messages at startup
        logger.info("Performing immediate cleanup of sent messages only...")
        delete_old_messages()
        logger.info("Immediate cleanup completed - only sent messages deleted")
       
        # Start the deletion worker thread
        delete_thread = Thread(target=deletion_worker, name="DeletionWorker", daemon=True)
        delete_thread.start()
        logger.info("Deletion worker thread started")
       
        # Start the CSV monitor thread
        csv_monitor_thread = Thread(target=csv_monitor_worker, name="CSVMonitor", daemon=True)
        csv_monitor_thread.start()
        logger.info("CSV monitor thread started")
       
        # Start the old message checker thread
        old_checker_thread = Thread(target=old_message_checker_worker, name="OldMessageChecker", daemon=True)
        old_checker_thread.start()
        logger.info("Old message checker thread started")
       
        # Start the extraction worker thread
        extraction_thread = Thread(target=extraction_worker, name="ExtractionWorker", daemon=True)
        extraction_thread.start()
        logger.info("Extraction worker thread started")
       
        logger.info("All worker threads are now running")
       
        # Keep the main thread alive
        while True:
            time.sleep(1)
           
    except KeyboardInterrupt:
        logger.info("Process stopped by user")
    except Exception as e:
        logger.error(f"Error in main process: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    main()